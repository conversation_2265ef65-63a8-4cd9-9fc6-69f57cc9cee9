import { useAuth } from '@/hooks/useAuth';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { supabase } from '@/integrations/supabase/client';
import { useState, useEffect } from 'react';

const AuthDebug = () => {
  const { user, profile, loading, isAdmin, isCustomer } = useAuth();
  const [promoting, setPromoting] = useState(false);
  const [message, setMessage] = useState('');
  const [directCheck, setDirectCheck] = useState<any>(null);
  const [checkingDirect, setCheckingDirect] = useState(false);

  // Direct database check bypass useAuth
  const checkDirectly = async () => {
    setCheckingDirect(true);
    try {
      const { data: { session } } = await supabase.auth.getSession();
      console.log('Direct session check:', session);

      if (session?.user) {
        const { data: profile, error } = await supabase
          .from('user_profiles')
          .select('*')
          .eq('id', session.user.id)
          .single();

        console.log('Direct profile check:', { profile, error });
        setDirectCheck({ session, profile, error });
      } else {
        setDirectCheck({ session: null, profile: null, error: 'No session' });
      }
    } catch (err) {
      console.error('Direct check error:', err);
      setDirectCheck({ error: err });
    } finally {
      setCheckingDirect(false);
    }
  };

  useEffect(() => {
    checkDirectly();
  }, []);

  const promoteToAdmin = async () => {
    const userId = user?.id || directCheck?.session?.user?.id;
    if (!userId) return;

    setPromoting(true);
    setMessage('');

    try {
      const { error } = await supabase
        .from('user_profiles')
        .upsert({
          id: userId,
          role: 'super_admin',
          first_name: 'Admin',
          last_name: 'User'
        });

      if (error) {
        setMessage(`Error: ${error.message}`);
      } else {
        setMessage('Successfully promoted to admin! Refresh the page.');
        // Refresh direct check
        checkDirectly();
      }
    } catch (err) {
      setMessage(`Error: ${err}`);
    } finally {
      setPromoting(false);
    }
  };

  return (
    <div className="container mx-auto p-8">
      <Card>
        <CardHeader>
          <CardTitle>Authentication Debug</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="bg-blue-50 p-4 rounded">
            <h3 className="font-semibold text-blue-800">useAuth Hook Status:</h3>
            <p>Loading: {loading ? 'Yes' : 'No'}</p>
            <p>Logged in: {user ? 'Yes' : 'No'}</p>
            {user && (
              <>
                <p>Email: {user.email}</p>
                <p>User ID: {user.id}</p>
              </>
            )}
            <p>Profile exists: {profile ? 'Yes' : 'No'}</p>
            {profile && (
              <>
                <p>Role: {profile.role}</p>
                <p>Name: {profile.first_name} {profile.last_name}</p>
              </>
            )}
            <p>Is Admin: {isAdmin ? 'Yes' : 'No'}</p>
            <p>Is Customer: {isCustomer ? 'Yes' : 'No'}</p>
          </div>

          <div className="bg-green-50 p-4 rounded">
            <h3 className="font-semibold text-green-800">Direct Database Check:</h3>
            <Button
              onClick={checkDirectly}
              disabled={checkingDirect}
              className="mb-2"
            >
              {checkingDirect ? 'Checking...' : 'Refresh Direct Check'}
            </Button>
            {directCheck && (
              <div className="text-sm">
                <p>Session exists: {directCheck.session ? 'Yes' : 'No'}</p>
                {directCheck.session && (
                  <>
                    <p>Email: {directCheck.session.user.email}</p>
                    <p>User ID: {directCheck.session.user.id}</p>
                  </>
                )}
                <p>Profile exists: {directCheck.profile ? 'Yes' : 'No'}</p>
                {directCheck.profile && (
                  <>
                    <p>Role: {directCheck.profile.role}</p>
                    <p>Name: {directCheck.profile.first_name} {directCheck.profile.last_name}</p>
                  </>
                )}
                {directCheck.error && (
                  <p className="text-red-600">Error: {JSON.stringify(directCheck.error)}</p>
                )}
              </div>
            )}
          </div>

          <div className="pt-4">
            {(user || directCheck?.session) && (
              <Button
                onClick={promoteToAdmin}
                disabled={promoting}
                className="bg-red-600 hover:bg-red-700 mr-4"
              >
                {promoting ? 'Promoting...' : 'Promote to Admin'}
              </Button>
            )}

            {message && (
              <p className={`mt-2 ${message.includes('Error') ? 'text-red-600' : 'text-green-600'}`}>
                {message}
              </p>
            )}
          </div>

          {!user && !directCheck?.session && (
            <div className="pt-4">
              <p className="text-amber-600">
                You need to log in first. Go to <a href="/login" className="underline">/login</a>
              </p>
            </div>
          )}

          {(isAdmin || directCheck?.profile?.role === 'super_admin') && (
            <div className="pt-4">
              <p className="text-green-600">
                ✅ You are an admin! You can access <a href="/admin" className="underline">/admin</a>
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default AuthDebug;
