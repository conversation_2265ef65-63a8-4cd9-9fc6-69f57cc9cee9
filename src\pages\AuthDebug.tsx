import { useAuth } from '@/hooks/useAuth';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { supabase } from '@/integrations/supabase/client';
import { useState } from 'react';

const AuthDebug = () => {
  const { user, profile, loading, isAdmin, isCustomer } = useAuth();
  const [promoting, setPromoting] = useState(false);
  const [message, setMessage] = useState('');

  const promoteToAdmin = async () => {
    if (!user) return;
    
    setPromoting(true);
    setMessage('');
    
    try {
      const { error } = await supabase
        .from('user_profiles')
        .upsert({
          id: user.id,
          role: 'super_admin',
          first_name: 'Admin',
          last_name: 'User'
        });

      if (error) {
        setMessage(`Error: ${error.message}`);
      } else {
        setMessage('Successfully promoted to admin! Refresh the page.');
      }
    } catch (err) {
      setMessage(`Error: ${err}`);
    } finally {
      setPromoting(false);
    }
  };

  if (loading) {
    return <div className="p-8">Loading...</div>;
  }

  return (
    <div className="container mx-auto p-8">
      <Card>
        <CardHeader>
          <CardTitle>Authentication Debug</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h3 className="font-semibold">User Status:</h3>
            <p>Logged in: {user ? 'Yes' : 'No'}</p>
            {user && (
              <>
                <p>Email: {user.email}</p>
                <p>User ID: {user.id}</p>
              </>
            )}
          </div>

          <div>
            <h3 className="font-semibold">Profile Status:</h3>
            <p>Profile exists: {profile ? 'Yes' : 'No'}</p>
            {profile && (
              <>
                <p>Role: {profile.role}</p>
                <p>Name: {profile.first_name} {profile.last_name}</p>
              </>
            )}
          </div>

          <div>
            <h3 className="font-semibold">Permissions:</h3>
            <p>Is Admin: {isAdmin ? 'Yes' : 'No'}</p>
            <p>Is Customer: {isCustomer ? 'Yes' : 'No'}</p>
          </div>

          {user && !isAdmin && (
            <div className="pt-4">
              <Button 
                onClick={promoteToAdmin} 
                disabled={promoting}
                className="bg-red-600 hover:bg-red-700"
              >
                {promoting ? 'Promoting...' : 'Promote to Admin'}
              </Button>
              {message && (
                <p className={`mt-2 ${message.includes('Error') ? 'text-red-600' : 'text-green-600'}`}>
                  {message}
                </p>
              )}
            </div>
          )}

          {!user && (
            <div className="pt-4">
              <p className="text-amber-600">
                You need to log in first. Go to <a href="/login" className="underline">/login</a>
              </p>
            </div>
          )}

          {isAdmin && (
            <div className="pt-4">
              <p className="text-green-600">
                ✅ You are an admin! You can access <a href="/admin" className="underline">/admin</a>
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default AuthDebug;
