-- Create admin user in auth.users table
-- This migration sets up the default admin user

-- Insert admin user into auth.users if it doesn't exist
DO $$
DECLARE
  admin_user_id uuid := '00000000-0000-0000-0000-000000000001';
  admin_email text := '<EMAIL>';
  admin_password text := 'admin123456';
  encrypted_password text;
BEGIN
  -- Check if admin user already exists
  IF NOT EXISTS (SELECT 1 FROM auth.users WHERE email = admin_email) THEN
    -- Generate encrypted password (this is a simplified version)
    -- In production, you should use proper password hashing
    encrypted_password := crypt(admin_password, gen_salt('bf'));
    
    -- Insert admin user into auth.users
    INSERT INTO auth.users (
      id,
      instance_id,
      email,
      encrypted_password,
      email_confirmed_at,
      created_at,
      updated_at,
      role,
      aud,
      confirmation_token,
      email_change_token_new,
      recovery_token
    ) VALUES (
      admin_user_id,
      '00000000-0000-0000-0000-000000000000',
      admin_email,
      encrypted_password,
      NOW(),
      NOW(),
      NOW(),
      'authenticated',
      'authenticated',
      '',
      '',
      ''
    );
    
    -- Insert admin identity
    INSERT INTO auth.identities (
      id,
      user_id,
      identity_data,
      provider,
      created_at,
      updated_at
    ) VALUES (
      admin_user_id,
      admin_user_id,
      format('{"sub":"%s","email":"%s"}', admin_user_id, admin_email)::jsonb,
      'email',
      NOW(),
      NOW()
    );
  END IF;
  
  -- Create/update admin profile
  INSERT INTO public.user_profiles (
    id,
    email,
    first_name,
    last_name,
    role,
    created_at,
    updated_at
  )
  VALUES (
    admin_user_id,
    admin_email,
    'Admin',
    'User',
    'super_admin'::user_role,
    NOW(),
    NOW()
  )
  ON CONFLICT (id) DO UPDATE SET
    role = 'super_admin'::user_role,
    updated_at = NOW();
    
END $$;
