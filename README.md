# Depth Perception Aquascapes

A comprehensive e-commerce platform for custom aquarium decor and aquascaping services.

## 🌊 About

Depth Perception Aquascapes is a full-featured e-commerce website specializing in custom aquarium decorations, coral replicas, and aquascaping services. The platform combines an artist portfolio showcase with a complete online store, featuring advanced inventory management, order processing, and customer management capabilities.

## ✨ Features

### 🛍️ E-commerce Platform
- **Product Catalog** - Browse and search custom aquarium decor
- **Shopping Cart** - Add items, manage quantities, and checkout
- **Order Management** - Track orders from placement to delivery
- **Customer Accounts** - User registration, login, and order history
- **Inventory Tracking** - Real-time stock management
- **Payment Processing** - Secure checkout system (ready for Stripe/PayPal integration)

### 🎨 Portfolio & Gallery
- **Artist Showcase** - Display custom aquascaping work
- **Portfolio Management** - Admin tools for managing gallery items
- **Featured Content** - Highlight best work and products

### 👨‍💼 Admin Dashboard
- **Order Management** - Process and track customer orders
- **Product Management** - Add, edit, and manage product catalog
- **Customer Management** - View and manage customer accounts
- **Content Management** - Update homepage and portfolio content
- **Analytics Dashboard** - Track sales and performance metrics

### 🔐 Authentication & Security
- **User Authentication** - Secure login and registration
- **Role-Based Access** - Customer, Admin, and Super Admin roles
- **Protected Routes** - Secure admin and customer areas
- **Data Security** - Row-level security with Supabase

## 🚀 Technology Stack

### Frontend
- **React 18** with TypeScript
- **Vite** for fast development and building
- **Tailwind CSS** for styling
- **shadcn/ui** component library
- **React Router** for navigation
- **React Query** for data fetching

### Backend & Database
- **Supabase** for backend services
- **PostgreSQL** database with advanced schema
- **Row Level Security (RLS)** for data protection
- **Real-time subscriptions** capability
- **File storage** for images

### Key Libraries
- **Lucide React** for icons
- **React Hook Form** for form handling
- **Sonner** for toast notifications
- **Date-fns** for date manipulation

## 🛠️ Installation & Setup

### Prerequisites
- Node.js 18+ and npm
- Git

### Local Development

1. **Clone the repository**
```bash
git clone <repository-url>
cd depth-perception-aquascapes
```

2. **Install dependencies**
```bash
npm install
```

3. **Environment Setup**
Create a `.env.local` file with your Supabase credentials:
```env
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

4. **Start development server**
```bash
npm run dev
```

5. **Set up admin access**
Navigate to `http://localhost:8080/admin-setup` to create an admin user.

### Database Setup

The application includes a comprehensive database migration file at `supabase/migrations/**************-ecommerce-schema.sql` that sets up:
- User management tables
- Product catalog with categories and attributes
- Shopping cart and order management
- Payment tracking
- Content management tables

## 📱 Usage

### For Customers
1. **Browse Products** - Visit the shop to see available aquarium decor
2. **View Portfolio** - Check out custom work in the gallery
3. **Create Account** - Register for order tracking and faster checkout
4. **Place Orders** - Add items to cart and complete secure checkout
5. **Track Orders** - Monitor order status in customer dashboard

### For Administrators
1. **Admin Setup** - Use `/admin-setup` to create admin account
2. **Admin Dashboard** - Access comprehensive management tools at `/admin`
3. **Manage Content** - Update homepage, portfolio, and product catalog
4. **Process Orders** - Handle customer orders and track fulfillment
5. **Customer Support** - View customer information and order history

## 🔧 Configuration

### Admin Access
- **Setup URL**: `/admin-setup`
- **Admin Panel**: `/admin`
- **Default Admin**: <EMAIL> / admin123456

### Key Features Configuration
- **Tax Rate**: 8% (configurable in cart hook)
- **Shipping**: $9.99 (free over $100)
- **Currency**: USD
- **Order Number Format**: DP + YYYYMMDD + sequence

## 🚀 Deployment

### Build for Production
```bash
npm run build
```

### Deploy Options
- **Vercel** - Connect your Git repository for automatic deployments
- **Netlify** - Deploy with continuous integration
- **Traditional Hosting** - Upload the `dist` folder to any web server

### Environment Variables for Production
Ensure these are set in your hosting platform:
- `VITE_SUPABASE_URL`
- `VITE_SUPABASE_ANON_KEY`

## 🔮 Future Enhancements

### Planned Features
- **Payment Integration** - Stripe and PayPal integration
- **Email Notifications** - Order confirmations and updates
- **Advanced Analytics** - Detailed sales and customer insights
- **SEO Optimization** - Meta tags and structured data
- **Mobile App** - React Native companion app
- **Multi-language Support** - Internationalization
- **Advanced Search** - Elasticsearch integration
- **Reviews & Ratings** - Customer feedback system

### Technical Improvements
- **Performance Optimization** - Image optimization and caching
- **Testing Suite** - Unit and integration tests
- **CI/CD Pipeline** - Automated testing and deployment
- **Monitoring** - Error tracking and performance monitoring

## 📄 License

This project is proprietary software for Depth Perception Aquascapes.

## 🤝 Support

For technical support or questions about the platform, please contact the development team.

---

**Depth Perception Aquascapes** - Transforming aquariums with custom artistry 🐠✨
