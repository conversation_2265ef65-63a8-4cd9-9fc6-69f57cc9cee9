import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { supabase } from '@/integrations/supabase/client';

const SupabaseTest = () => {
  const [results, setResults] = useState<any>({});
  const [testing, setTesting] = useState(false);

  const runTests = async () => {
    setTesting(true);
    const testResults: any = {};

    try {
      // Test 1: Basic connection
      console.log('Testing basic connection...');
      testResults.connection = 'Testing...';
      setResults({ ...testResults });

      // Test 2: Check session
      console.log('Testing session...');
      const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
      testResults.session = {
        success: !sessionError,
        data: sessionData?.session?.user?.email || 'No session',
        error: sessionError?.message
      };
      setResults({ ...testResults });

      // Test 3: Test database query
      console.log('Testing database query...');
      const { data: profileData, error: profileError } = await supabase
        .from('user_profiles')
        .select('*')
        .limit(1);
      
      testResults.database = {
        success: !profileError,
        data: profileData ? `Found ${profileData.length} profiles` : 'No data',
        error: profileError?.message
      };
      setResults({ ...testResults });

      // Test 4: Test auth users (if we have session)
      if (sessionData?.session?.user) {
        console.log('Testing user profile fetch...');
        const { data: userProfile, error: userError } = await supabase
          .from('user_profiles')
          .select('*')
          .eq('id', sessionData.session.user.id)
          .single();
        
        testResults.userProfile = {
          success: !userError,
          data: userProfile ? `Role: ${userProfile.role}` : 'No profile',
          error: userError?.message
        };
      } else {
        testResults.userProfile = {
          success: false,
          data: 'No session to test with',
          error: 'Not logged in'
        };
      }
      setResults({ ...testResults });

      testResults.connection = 'Complete';
      setResults({ ...testResults });

    } catch (error) {
      console.error('Test error:', error);
      testResults.connection = `Error: ${error}`;
      setResults({ ...testResults });
    } finally {
      setTesting(false);
    }
  };

  const testLogin = async () => {
    setTesting(true);
    try {
      console.log('Testing login with admin credentials...');
      const { data, error } = await supabase.auth.signInWithPassword({
        email: '<EMAIL>',
        password: 'admin123456'
      });
      
      setResults(prev => ({
        ...prev,
        login: {
          success: !error,
          data: data?.user?.email || 'No user returned',
          error: error?.message
        }
      }));
    } catch (err) {
      setResults(prev => ({
        ...prev,
        login: {
          success: false,
          data: 'Exception occurred',
          error: String(err)
        }
      }));
    } finally {
      setTesting(false);
    }
  };

  return (
    <div className="container mx-auto p-8">
      <Card>
        <CardHeader>
          <CardTitle>Supabase Connection Test</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-4">
            <Button onClick={runTests} disabled={testing}>
              {testing ? 'Testing...' : 'Run Connection Tests'}
            </Button>
            <Button onClick={testLogin} disabled={testing} variant="outline">
              Test Login
            </Button>
          </div>

          {Object.keys(results).length > 0 && (
            <div className="space-y-4">
              {Object.entries(results).map(([test, result]: [string, any]) => (
                <div key={test} className="border p-4 rounded">
                  <h3 className="font-semibold capitalize">{test}:</h3>
                  {typeof result === 'object' ? (
                    <div className="ml-4">
                      <p className={result.success ? 'text-green-600' : 'text-red-600'}>
                        Status: {result.success ? 'Success' : 'Failed'}
                      </p>
                      <p>Data: {result.data}</p>
                      {result.error && <p className="text-red-600">Error: {result.error}</p>}
                    </div>
                  ) : (
                    <p>{result}</p>
                  )}
                </div>
              ))}
            </div>
          )}

          <div className="mt-6 p-4 bg-gray-50 rounded">
            <h3 className="font-semibold">Instructions:</h3>
            <ol className="list-decimal list-inside space-y-1 text-sm">
              <li>Click "Run Connection Tests" to test basic Supabase connectivity</li>
              <li>Click "Test Login" to test authentication with admin credentials</li>
              <li>Check browser console (F12) for detailed logs</li>
              <li>If tests fail, check your Supabase project settings</li>
            </ol>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SupabaseTest;
