-- SIMPLE FIX: Update existing admin user password
-- Run this in Supabase SQL Editor (http://localhost:54323)

-- Method 1: Update the existing user's password hash
-- This is the bcrypt hash for 'admin123456'
UPDATE auth.users
SET encrypted_password = '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi'
WHERE email = '<EMAIL>';

-- Method 2: If the above doesn't work, delete and recreate
-- Uncomment the lines below if Method 1 fails:

/*
-- Delete existing admin user
DELETE FROM public.user_profiles WHERE id IN (
  SELECT id FROM auth.users WHERE email = '<EMAIL>'
);
DELETE FROM auth.users WHERE email = '<EMAIL>';

-- Create new admin user (you'll need to sign up through the app first, then run this)
-- After signing <NAME_EMAIL>, run:
UPDATE public.user_profiles
SET role = 'admin', first_name = 'Admin', last_name = 'User'
WHERE id = (SELECT id FROM auth.users WHERE email = '<EMAIL>');
*/

-- Verify the setup
SELECT
  au.email,
  au.encrypted_password,
  up.role,
  up.first_name,
  up.last_name
FROM auth.users au
LEFT JOIN public.user_profiles up ON au.id = up.id
WHERE au.email = '<EMAIL>';
