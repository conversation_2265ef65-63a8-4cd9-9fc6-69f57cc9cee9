import { useEffect, useState } from 'react';
import { User, Session } from '@supabase/supabase-js';
import { supabase } from '@/integrations/supabase/client';
import { Database } from '@/integrations/supabase/types';

type UserProfile = Database['public']['Tables']['user_profiles']['Row'];
type UserRole = Database['public']['Enums']['user_role'];

interface AuthState {
  user: User | null;
  profile: UserProfile | null;
  session: Session | null;
  loading: boolean;
  isAdmin: boolean;
  isCustomer: boolean;
}

export const useAuth = () => {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    profile: null,
    session: null,
    loading: true,
    isAdmin: false,
    isCustomer: false,
  });

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      try {
        console.log('Getting initial session...');
        const { data: { session } } = await supabase.auth.getSession();
        console.log('Initial session:', session?.user?.email || 'No session');
        await handleAuthChange(session);
      } catch (error) {
        console.error('Error getting initial session:', error);
        setAuthState(prev => ({ ...prev, loading: false }));
      }
    };

    getInitialSession();

    // Set a timeout to prevent infinite loading
    const timeout = setTimeout(() => {
      console.log('Auth timeout reached, stopping loading');
      setAuthState(prev => ({ ...prev, loading: false }));
    }, 10000); // 10 second timeout

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('Auth state change event:', event);
      await handleAuthChange(session);
    });

    return () => {
      subscription.unsubscribe();
      clearTimeout(timeout);
    };
  }, []);

  const handleAuthChange = async (session: Session | null) => {
    console.log('Auth change triggered:', session?.user?.email || 'No user');
    setAuthState(prev => ({ ...prev, loading: true }));

    if (session?.user) {
      console.log('Fetching user profile for:', session.user.id);
      // Fetch user profile
      const { data: profile, error } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', session.user.id)
        .single();

      console.log('Profile fetch result:', { profile, error });

      if (error && error.code === 'PGRST116') {
        console.log('Profile not found, creating new profile');
        // Profile doesn't exist, create one
        const { data: newProfile, error: createError } = await supabase
          .from('user_profiles')
          .insert({
            id: session.user.id,
            role: 'customer',
            first_name: session.user.user_metadata?.first_name || null,
            last_name: session.user.user_metadata?.last_name || null,
          })
          .select()
          .single();

        console.log('Profile creation result:', { newProfile, createError });

        if (!createError && newProfile) {
          console.log('Setting auth state with new profile');
          setAuthState({
            user: session.user,
            profile: newProfile,
            session,
            loading: false,
            isAdmin: newProfile.role === 'admin' || newProfile.role === 'super_admin',
            isCustomer: newProfile.role === 'customer',
          });
        } else {
          console.log('Profile creation failed, setting default state');
          setAuthState({
            user: session.user,
            profile: null,
            session,
            loading: false,
            isAdmin: false,
            isCustomer: true,
          });
        }
      } else if (!error && profile) {
        console.log('Profile found, setting auth state:', profile);
        setAuthState({
          user: session.user,
          profile,
          session,
          loading: false,
          isAdmin: profile.role === 'admin' || profile.role === 'super_admin',
          isCustomer: profile.role === 'customer',
        });
      } else {
        console.log('Profile fetch error or no profile:', error);
        setAuthState({
          user: session.user,
          profile: null,
          session,
          loading: false,
          isAdmin: false,
          isCustomer: true,
        });
      }
    } else {
      console.log('No session, setting logged out state');
      setAuthState({
        user: null,
        profile: null,
        session: null,
        loading: false,
        isAdmin: false,
        isCustomer: false,
      });
    }
  };

  const signIn = async (email: string, password: string) => {
    console.log('Attempting sign in for:', email);
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });
      console.log('Sign in result:', { data: data?.user?.email, error });
      return { data, error };
    } catch (err) {
      console.error('Sign in exception:', err);
      return { data: null, error: err };
    }
  };

  const signUp = async (email: string, password: string, userData?: {
    first_name?: string;
    last_name?: string;
    phone?: string;
  }) => {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: userData,
      },
    });
    return { data, error };
  };

  const signOut = async () => {
    const { error } = await supabase.auth.signOut();
    return { error };
  };

  const resetPassword = async (email: string) => {
    const { data, error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/reset-password`,
    });
    return { data, error };
  };

  const updatePassword = async (password: string) => {
    const { data, error } = await supabase.auth.updateUser({
      password,
    });
    return { data, error };
  };

  const updateProfile = async (updates: Partial<UserProfile>) => {
    if (!authState.user) return { error: new Error('Not authenticated') };

    const { data, error } = await supabase
      .from('user_profiles')
      .update(updates)
      .eq('id', authState.user.id)
      .select()
      .single();

    if (!error && data) {
      setAuthState(prev => ({
        ...prev,
        profile: data,
        isAdmin: data.role === 'admin' || data.role === 'super_admin',
        isCustomer: data.role === 'customer',
      }));
    }

    return { data, error };
  };

  return {
    ...authState,
    signIn,
    signUp,
    signOut,
    resetPassword,
    updatePassword,
    updateProfile,
  };
};

export default useAuth;
