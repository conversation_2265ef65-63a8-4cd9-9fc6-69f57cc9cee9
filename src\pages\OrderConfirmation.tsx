import { useEffect, useState } from 'react';
import { Link, useSearchParams } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  CheckCircle, 
  Package, 
  Mail, 
  Home,
  Download,
  ArrowRight
} from 'lucide-react';
import Navbar from '@/components/Navbar';

const OrderConfirmation = () => {
  const [searchParams] = useSearchParams();
  const orderNumber = searchParams.get('order');
  const [emailSent, setEmailSent] = useState(false);

  useEffect(() => {
    // Simulate sending confirmation email
    const timer = setTimeout(() => {
      setEmailSent(true);
    }, 2000);

    return () => clearTimeout(timer);
  }, []);

  if (!orderNumber) {
    return (
      <div className="min-h-screen bg-background">
        <Navbar />
        <div className="container mx-auto px-4 py-8">
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-12">
              <h3 className="text-xl font-medium mb-2">Order not found</h3>
              <p className="text-muted-foreground mb-6">
                We couldn't find the order you're looking for.
              </p>
              <Link to="/shop">
                <Button>
                  Continue Shopping
                </Button>
              </Link>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <Navbar />
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto space-y-6">
          {/* Success Header */}
          <Card>
            <CardContent className="pt-6">
              <div className="text-center space-y-4">
                <div className="flex justify-center">
                  <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                    <CheckCircle className="h-8 w-8 text-green-600" />
                  </div>
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-green-600 mb-2">
                    Order Confirmed!
                  </h1>
                  <p className="text-muted-foreground">
                    Thank you for your order. We've received your order and will begin processing it soon.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Order Details */}
          <Card>
            <CardHeader>
              <CardTitle>Order Details</CardTitle>
              <CardDescription>
                Order #{orderNumber}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium mb-2">Order Number</h4>
                  <p className="text-muted-foreground">{orderNumber}</p>
                </div>
                <div>
                  <h4 className="font-medium mb-2">Order Date</h4>
                  <p className="text-muted-foreground">
                    {new Date().toLocaleDateString()}
                  </p>
                </div>
              </div>

              <Separator />

              <div>
                <h4 className="font-medium mb-2">Order Status</h4>
                <Badge variant="secondary">
                  <Package className="h-3 w-3 mr-1" />
                  Processing
                </Badge>
              </div>
            </CardContent>
          </Card>

          {/* What's Next */}
          <Card>
            <CardHeader>
              <CardTitle>What's Next?</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-primary rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-xs text-primary-foreground font-bold">1</span>
                  </div>
                  <div>
                    <h4 className="font-medium">Order Processing</h4>
                    <p className="text-sm text-muted-foreground">
                      We're preparing your custom aquarium decor items. This typically takes 3-5 business days.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-muted rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-xs text-muted-foreground font-bold">2</span>
                  </div>
                  <div>
                    <h4 className="font-medium">Shipping</h4>
                    <p className="text-sm text-muted-foreground">
                      Once ready, we'll carefully package and ship your order. You'll receive tracking information.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-muted rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-xs text-muted-foreground font-bold">3</span>
                  </div>
                  <div>
                    <h4 className="font-medium">Delivery</h4>
                    <p className="text-sm text-muted-foreground">
                      Your beautiful aquarium decor will arrive ready to transform your underwater world!
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Email Confirmation */}
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center space-x-3">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                  emailSent ? 'bg-green-100' : 'bg-muted'
                }`}>
                  <Mail className={`h-4 w-4 ${
                    emailSent ? 'text-green-600' : 'text-muted-foreground'
                  }`} />
                </div>
                <div>
                  <h4 className="font-medium">
                    {emailSent ? 'Confirmation Email Sent' : 'Sending Confirmation Email...'}
                  </h4>
                  <p className="text-sm text-muted-foreground">
                    {emailSent 
                      ? 'Check your inbox for order details and tracking information.'
                      : 'We\'re sending you a confirmation email with all the details.'
                    }
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Actions */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardContent className="pt-6">
                <div className="text-center space-y-3">
                  <Package className="h-8 w-8 text-muted-foreground mx-auto" />
                  <div>
                    <h4 className="font-medium">Track Your Order</h4>
                    <p className="text-sm text-muted-foreground">
                      View order status and tracking information
                    </p>
                  </div>
                  <Link to="/orders" className="block">
                    <Button variant="outline" className="w-full">
                      View Orders
                      <ArrowRight className="h-4 w-4 ml-2" />
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="text-center space-y-3">
                  <Download className="h-8 w-8 text-muted-foreground mx-auto" />
                  <div>
                    <h4 className="font-medium">Download Invoice</h4>
                    <p className="text-sm text-muted-foreground">
                      Get a PDF copy of your order receipt
                    </p>
                  </div>
                  <Button variant="outline" className="w-full">
                    Download PDF
                    <Download className="h-4 w-4 ml-2" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Continue Shopping */}
          <Card>
            <CardContent className="pt-6">
              <div className="text-center space-y-4">
                <h3 className="text-lg font-medium">Continue Shopping</h3>
                <p className="text-muted-foreground">
                  Discover more unique aquarium decor to complete your underwater paradise.
                </p>
                <div className="flex flex-col sm:flex-row gap-3 justify-center">
                  <Link to="/shop">
                    <Button>
                      Browse Products
                      <ArrowRight className="h-4 w-4 ml-2" />
                    </Button>
                  </Link>
                  <Link to="/portfolio">
                    <Button variant="outline">
                      View Gallery
                    </Button>
                  </Link>
                  <Link to="/">
                    <Button variant="ghost">
                      <Home className="h-4 w-4 mr-2" />
                      Home
                    </Button>
                  </Link>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Support */}
          <Card>
            <CardContent className="pt-6">
              <div className="text-center space-y-2">
                <h4 className="font-medium">Need Help?</h4>
                <p className="text-sm text-muted-foreground">
                  Have questions about your order? We're here to help!
                </p>
                <Link to="/contact">
                  <Button variant="outline" size="sm">
                    Contact Support
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default OrderConfirmation;
