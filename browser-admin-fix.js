// Browser Console Admin Fix
// Copy and paste this into the browser console on the admin setup page

async function createAdminUserDirect() {
  const adminEmail = '<EMAIL>';
  const adminPassword = 'admin123456';

  try {
    console.log('Starting admin user creation...');
    
    // Get the supabase client from the window (if available)
    const supabase = window.supabase || (await import('/src/integrations/supabase/client.js')).supabase;
    
    if (!supabase) {
      throw new Error('Supabase client not found');
    }

    // Try to sign up the user
    console.log('Attempting signup...');
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email: adminEmail,
      password: adminPassword,
      options: {
        data: {
          first_name: 'Admin',
          last_name: 'User'
        }
      }
    });

    console.log('Signup result:', { authData, authError });

    // Handle case where user already exists
    let userId = authData?.user?.id;
    
    if (authError && authError.message.includes('already registered')) {
      console.log('User already exists, trying to sign in...');
      const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
        email: adminEmail,
        password: adminPassword
      });

      if (signInError) {
        throw new Error(`Cannot sign in: ${signInError.message}`);
      }

      userId = signInData?.user?.id;
      console.log('Signed in with user ID:', userId);
    } else if (authError) {
      throw authError;
    }

    if (!userId) {
      throw new Error('Could not get user ID');
    }

    // Create or update the user profile with admin role
    console.log('Creating/updating profile...');
    const { data: profileData, error: profileError } = await supabase
      .from('user_profiles')
      .upsert({
        id: userId,
        email: adminEmail,
        first_name: 'Admin',
        last_name: 'User',
        role: 'super_admin'
      })
      .select()
      .single();

    console.log('Profile result:', { profileData, profileError });

    if (profileError) {
      throw profileError;
    }

    console.log('✅ Admin user created successfully!');
    console.log('Email:', adminEmail);
    console.log('Password:', adminPassword);
    console.log('You can now login at /admin/login');

    return {
      success: true,
      message: 'Admin user created successfully!',
      email: adminEmail,
      password: adminPassword,
      userId: userId,
      profile: profileData
    };

  } catch (error) {
    console.error('❌ Error creating admin user:', error);
    return {
      success: false,
      error: error.message || 'Unknown error occurred'
    };
  }
}

// Run the function
createAdminUserDirect().then(result => {
  console.log('Final result:', result);
});
