// Browser Console Admin Fix
// Copy and paste this into the browser console on the admin login page

async function fixAdminLogin() {
  const adminEmail = '<EMAIL>';

  try {
    console.log('Fixing admin login...');

    // Import supabase client
    const { supabase } = await import('/src/integrations/supabase/client.js');

    if (!supabase) {
      throw new Error('Supabase client not found');
    }

    // First, get the user ID from auth.users
    console.log('Getting user from auth.users...');
    const { data: { users }, error: usersError } = await supabase.auth.admin.listUsers();

    if (usersError) {
      console.log('Cannot access admin API, trying direct query...');

      // Try to get user ID by signing in temporarily
      const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
        email: adminEmail,
        password: 'admin123456'
      });

      if (signInError) {
        throw new Error(`Cannot sign in: ${signInError.message}`);
      }

      const userId = signInData?.user?.id;
      if (!userId) {
        throw new Error('Could not get user ID');
      }

      console.log('Got user ID from sign in:', userId);

      // Sign out immediately
      await supabase.auth.signOut();

      // Create the profile
      await createProfile(supabase, userId, adminEmail);
      return;
    }

    const adminUser = users.find(u => u.email === adminEmail);
    if (!adminUser) {
      throw new Error('Admin user not found in auth.users');
    }

    console.log('Found admin user:', adminUser.id);
    await createProfile(supabase, adminUser.id, adminEmail);

    // Create or update the user profile with admin role
    console.log('Creating/updating profile...');
    const { data: profileData, error: profileError } = await supabase
      .from('user_profiles')
      .upsert({
        id: userId,
        email: adminEmail,
        first_name: 'Admin',
        last_name: 'User',
        role: 'super_admin'
      })
      .select()
      .single();

    console.log('Profile result:', { profileData, profileError });

    if (profileError) {
      throw profileError;
    }

    console.log('✅ Admin user created successfully!');
    console.log('Email:', adminEmail);
    console.log('Password:', adminPassword);
    console.log('You can now login at /admin/login');

    return {
      success: true,
      message: 'Admin user created successfully!',
      email: adminEmail,
      password: adminPassword,
      userId: userId,
      profile: profileData
    };

  } catch (error) {
    console.error('❌ Error creating admin user:', error);
    return {
      success: false,
      error: error.message || 'Unknown error occurred'
    };
  }
}

// Run the function
createAdminUserDirect().then(result => {
  console.log('Final result:', result);
});
