// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://fkcyycowirzxbegjozki.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZrY3l5Y293aXJ6eGJlZ2pvemtpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI2ODA1NjcsImV4cCI6MjA2ODI1NjU2N30.wbqPKkwn91ym8UKAHw_UpgDpORlOmYav4cH4BGgKzYI";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});