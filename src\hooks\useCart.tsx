import { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/useAuth';
import { useToast } from '@/hooks/use-toast';
import { Database } from '@/integrations/supabase/types';

type Product = Database['public']['Tables']['products']['Row'];
type CartItem = Database['public']['Tables']['cart_items']['Row'] & {
  product: Product;
};

interface CartContextType {
  items: CartItem[];
  loading: boolean;
  addToCart: (productId: string, quantity?: number) => Promise<void>;
  removeFromCart: (itemId: string) => Promise<void>;
  updateQuantity: (itemId: string, quantity: number) => Promise<void>;
  clearCart: () => Promise<void>;
  getTotalItems: () => number;
  getTotalPrice: () => number;
  getSubtotal: () => number;
  getTaxAmount: () => number;
  getShippingAmount: () => number;
}

const CartContext = createContext<CartContextType | undefined>(undefined);

export const useCart = () => {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
};

interface CartProviderProps {
  children: ReactNode;
}

export const CartProvider = ({ children }: CartProviderProps) => {
  const [items, setItems] = useState<CartItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [cartId, setCartId] = useState<string | null>(null);
  const { user } = useAuth();
  const { toast } = useToast();

  // Tax rate (8%)
  const TAX_RATE = 0.08;
  // Shipping rate
  const SHIPPING_RATE = 9.99;

  useEffect(() => {
    initializeCart();
  }, [user]);

  const initializeCart = async () => {
    try {
      let currentCartId = cartId;

      if (user) {
        // For authenticated users, get or create cart
        const { data: existingCart } = await supabase
          .from('shopping_carts')
          .select('id')
          .eq('user_id', user.id)
          .single();

        if (existingCart) {
          currentCartId = existingCart.id;
        } else {
          const { data: newCart, error } = await supabase
            .from('shopping_carts')
            .insert({ user_id: user.id })
            .select('id')
            .single();

          if (error) throw error;
          currentCartId = newCart.id;
        }
      } else {
        // For guest users, use session-based cart
        let sessionId = localStorage.getItem('cart_session_id');
        if (!sessionId) {
          sessionId = Math.random().toString(36).substring(2) + Date.now().toString(36);
          localStorage.setItem('cart_session_id', sessionId);
        }

        const { data: existingCart } = await supabase
          .from('shopping_carts')
          .select('id')
          .eq('session_id', sessionId)
          .single();

        if (existingCart) {
          currentCartId = existingCart.id;
        } else {
          const { data: newCart, error } = await supabase
            .from('shopping_carts')
            .insert({ session_id: sessionId })
            .select('id')
            .single();

          if (error) throw error;
          currentCartId = newCart.id;
        }
      }

      setCartId(currentCartId);
      if (currentCartId) {
        await fetchCartItems(currentCartId);
      }
    } catch (error) {
      console.error('Error initializing cart:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchCartItems = async (currentCartId: string) => {
    try {
      const { data, error } = await supabase
        .from('cart_items')
        .select(`
          *,
          product:products(*)
        `)
        .eq('cart_id', currentCartId);

      if (error) throw error;

      setItems(data as CartItem[] || []);
    } catch (error) {
      console.error('Error fetching cart items:', error);
    }
  };

  const addToCart = async (productId: string, quantity: number = 1) => {
    if (!cartId) {
      toast({
        title: "Error",
        description: "Cart not initialized",
        variant: "destructive",
      });
      return;
    }

    try {
      // Get product details
      const { data: product, error: productError } = await supabase
        .from('products')
        .select('*')
        .eq('id', productId)
        .single();

      if (productError) throw productError;

      if (!product.in_stock || (product.stock_quantity && product.stock_quantity <= 0)) {
        toast({
          title: "Out of Stock",
          description: "This product is currently out of stock",
          variant: "destructive",
        });
        return;
      }

      // Check if item already exists in cart
      const existingItem = items.find(item => item.product_id === productId);

      if (existingItem) {
        // Update quantity
        const newQuantity = existingItem.quantity + quantity;
        
        if (product.stock_quantity && newQuantity > product.stock_quantity) {
          toast({
            title: "Insufficient Stock",
            description: `Only ${product.stock_quantity} items available`,
            variant: "destructive",
          });
          return;
        }

        await updateQuantity(existingItem.id, newQuantity);
      } else {
        // Add new item
        const { error } = await supabase
          .from('cart_items')
          .insert({
            cart_id: cartId,
            product_id: productId,
            quantity,
            unit_price: product.sale_price || product.price,
          });

        if (error) throw error;

        await fetchCartItems(cartId);
      }

      toast({
        title: "Added to Cart",
        description: `${product.name} has been added to your cart`,
      });
    } catch (error) {
      console.error('Error adding to cart:', error);
      toast({
        title: "Error",
        description: "Failed to add item to cart",
        variant: "destructive",
      });
    }
  };

  const removeFromCart = async (itemId: string) => {
    try {
      const { error } = await supabase
        .from('cart_items')
        .delete()
        .eq('id', itemId);

      if (error) throw error;

      setItems(prev => prev.filter(item => item.id !== itemId));

      toast({
        title: "Removed from Cart",
        description: "Item has been removed from your cart",
      });
    } catch (error) {
      console.error('Error removing from cart:', error);
      toast({
        title: "Error",
        description: "Failed to remove item from cart",
        variant: "destructive",
      });
    }
  };

  const updateQuantity = async (itemId: string, quantity: number) => {
    if (quantity <= 0) {
      await removeFromCart(itemId);
      return;
    }

    try {
      const item = items.find(i => i.id === itemId);
      if (!item) return;

      // Check stock availability
      if (item.product.stock_quantity && quantity > item.product.stock_quantity) {
        toast({
          title: "Insufficient Stock",
          description: `Only ${item.product.stock_quantity} items available`,
          variant: "destructive",
        });
        return;
      }

      const { error } = await supabase
        .from('cart_items')
        .update({ quantity })
        .eq('id', itemId);

      if (error) throw error;

      setItems(prev => 
        prev.map(item => 
          item.id === itemId ? { ...item, quantity } : item
        )
      );
    } catch (error) {
      console.error('Error updating quantity:', error);
      toast({
        title: "Error",
        description: "Failed to update quantity",
        variant: "destructive",
      });
    }
  };

  const clearCart = async () => {
    if (!cartId) return;

    try {
      const { error } = await supabase
        .from('cart_items')
        .delete()
        .eq('cart_id', cartId);

      if (error) throw error;

      setItems([]);

      toast({
        title: "Cart Cleared",
        description: "All items have been removed from your cart",
      });
    } catch (error) {
      console.error('Error clearing cart:', error);
      toast({
        title: "Error",
        description: "Failed to clear cart",
        variant: "destructive",
      });
    }
  };

  const getTotalItems = () => {
    return items.reduce((total, item) => total + item.quantity, 0);
  };

  const getSubtotal = () => {
    return items.reduce((total, item) => total + (item.unit_price * item.quantity), 0);
  };

  const getTaxAmount = () => {
    return getSubtotal() * TAX_RATE;
  };

  const getShippingAmount = () => {
    // Free shipping over $100
    return getSubtotal() >= 100 ? 0 : SHIPPING_RATE;
  };

  const getTotalPrice = () => {
    return getSubtotal() + getTaxAmount() + getShippingAmount();
  };

  const value: CartContextType = {
    items,
    loading,
    addToCart,
    removeFromCart,
    updateQuantity,
    clearCart,
    getTotalItems,
    getTotalPrice,
    getSubtotal,
    getTaxAmount,
    getShippingAmount,
  };

  return (
    <CartContext.Provider value={value}>
      {children}
    </CartContext.Provider>
  );
};
