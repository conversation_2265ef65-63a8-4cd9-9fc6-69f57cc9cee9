import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Shield, User, CheckCircle } from 'lucide-react';
import { createDefaultAdminUser, promoteUserToAdmin } from '@/utils/createAdminUser';
import { useToast } from '@/hooks/use-toast';

const AdminSetup = () => {
  const [loading, setLoading] = useState(false);
  const [userEmail, setUserEmail] = useState('');
  const [result, setResult] = useState<any>(null);
  const { toast } = useToast();

  const handleCreateDefaultAdmin = async () => {
    setLoading(true);
    setResult(null);

    try {
      const result = await createDefaultAdminUser();
      setResult(result);
      
      if (result.success) {
        toast({
          title: "Success!",
          description: result.message,
        });
      } else {
        toast({
          title: "Error",
          description: result.error,
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create admin user",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handlePromoteUser = async () => {
    if (!userEmail) {
      toast({
        title: "Error",
        description: "Please enter a user email",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);
    setResult(null);

    try {
      const result = await promoteUserToAdmin(userEmail);
      setResult(result);
      
      if (result.success) {
        toast({
          title: "Success!",
          description: result.message,
        });
      } else {
        toast({
          title: "Error",
          description: result.error,
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to promote user",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4">
      <div className="w-full max-w-2xl space-y-6">
        <div className="text-center space-y-2">
          <Shield className="h-12 w-12 mx-auto text-primary" />
          <h1 className="text-3xl font-bold">Admin Setup</h1>
          <p className="text-muted-foreground">
            Set up admin access for Depth Perception Aquascapes
          </p>
        </div>

        {/* Create Default Admin */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <User className="h-5 w-5 mr-2" />
              Create Default Admin User
            </CardTitle>
            <CardDescription>
              Creates a default admin user for immediate access to the admin panel
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Alert>
              <AlertDescription>
                This will create an admin user with the following credentials:
                <br />
                <strong>Email:</strong> <EMAIL>
                <br />
                <strong>Password:</strong> admin123456
                <br />
                <em>Please change these credentials after first login!</em>
              </AlertDescription>
            </Alert>

            <Button 
              onClick={handleCreateDefaultAdmin} 
              disabled={loading}
              className="w-full"
            >
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating Admin User...
                </>
              ) : (
                <>
                  <Shield className="mr-2 h-4 w-4" />
                  Create Default Admin
                </>
              )}
            </Button>
          </CardContent>
        </Card>

        {/* Promote Existing User */}
        <Card>
          <CardHeader>
            <CardTitle>Promote Existing User to Admin</CardTitle>
            <CardDescription>
              Enter the email of an existing user to promote them to admin
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="userEmail">User Email</Label>
              <Input
                id="userEmail"
                type="email"
                placeholder="<EMAIL>"
                value={userEmail}
                onChange={(e) => setUserEmail(e.target.value)}
              />
            </div>

            <Button 
              onClick={handlePromoteUser} 
              disabled={loading || !userEmail}
              variant="outline"
              className="w-full"
            >
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Promoting User...
                </>
              ) : (
                <>
                  <User className="mr-2 h-4 w-4" />
                  Promote to Admin
                </>
              )}
            </Button>
          </CardContent>
        </Card>

        {/* Result Display */}
        {result && (
          <Card>
            <CardContent className="pt-6">
              {result.success ? (
                <div className="flex items-center space-x-2 text-green-600">
                  <CheckCircle className="h-5 w-5" />
                  <span className="font-medium">{result.message}</span>
                </div>
              ) : (
                <div className="text-red-600">
                  <strong>Error:</strong> {result.error}
                </div>
              )}
              
              {result.success && result.email && result.password && (
                <div className="mt-4 p-4 bg-muted rounded-lg">
                  <h4 className="font-medium mb-2">Admin Credentials:</h4>
                  <p><strong>Email:</strong> {result.email}</p>
                  <p><strong>Password:</strong> {result.password}</p>
                  <p className="text-sm text-muted-foreground mt-2">
                    You can now login and access the admin panel at: 
                    <a href="/admin" className="text-primary hover:underline ml-1">
                      /admin
                    </a>
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Instructions */}
        <Card>
          <CardHeader>
            <CardTitle>Next Steps</CardTitle>
          </CardHeader>
          <CardContent>
            <ol className="list-decimal list-inside space-y-2 text-sm">
              <li>Create or promote an admin user using the options above</li>
              <li>Login with the admin credentials</li>
              <li>Navigate to <code className="bg-muted px-1 rounded">/admin</code> to access the admin panel</li>
              <li>Change the default password if you used the default admin option</li>
              <li>Start managing your e-commerce platform!</li>
            </ol>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AdminSetup;
