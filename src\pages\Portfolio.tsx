import { useEffect, useState } from "react";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { supabase } from "@/integrations/supabase/client";
import Navbar from "@/components/Navbar";
import FishAnimation from "@/components/FishAnimation";
import { ArrowLeft } from "lucide-react";
import { Link } from "react-router-dom";

interface ArtistProfile {
  id: string;
  name: string;
  bio: string;
  full_biography: string;
  portrait_url: string;
  hero_image_url: string;
}

interface PortfolioItem {
  id: string;
  title: string;
  description: string;
  image_url: string;
  category: string;
}

const Portfolio = () => {
  const [artist, setArtist] = useState<ArtistProfile | null>(null);
  const [portfolioItems, setPortfolioItems] = useState<PortfolioItem[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('All');

  useEffect(() => {
    const fetchData = async () => {
      // Fetch artist profile
      const { data: artistData } = await supabase
        .from('artist_profile')
        .select('*')
        .single();
      
      if (artistData) setArtist(artistData);

      // Fetch all portfolio items
      const { data: portfolioData } = await supabase
        .from('portfolio_items')
        .select('*')
        .order('sort_order');
      
      if (portfolioData) setPortfolioItems(portfolioData);
    };

    fetchData();
  }, []);

  const categories = ['All', ...new Set(portfolioItems.map(item => item.category))];
  const filteredItems = selectedCategory === 'All' 
    ? portfolioItems 
    : portfolioItems.filter(item => item.category === selectedCategory);

  return (
    <div className="min-h-screen bg-background">
      <Navbar />
      
      {/* Hero Section with Artist */}
      {artist && (
        <section className="relative pt-20 pb-12">
          <div 
            className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-20"
            style={{ backgroundImage: `url(${artist.hero_image_url})` }}
          />
          <div className="absolute inset-0 wave-overlay" />
          
          <div className="relative z-10 container mx-auto px-4">
            <Link to="/" className="inline-flex items-center text-muted-foreground hover:text-primary mb-8 transition-colors">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Home
            </Link>
            
            <div className="max-w-4xl mx-auto">
              <div className="flex flex-col md:flex-row items-center gap-8 bg-card/80 backdrop-blur-sm rounded-lg p-8">
                <Avatar className="w-40 h-40 mx-auto md:mx-0">
                  <AvatarImage src={artist.portrait_url} alt={artist.name} />
                  <AvatarFallback className="text-2xl">{artist.name[0]}</AvatarFallback>
                </Avatar>
                <div className="flex-1 text-center md:text-left">
                  <h1 className="text-4xl font-bold mb-4">{artist.name}</h1>
                  <h2 className="text-xl text-muted-foreground mb-6">Artist Portfolio</h2>
                  <p className="text-muted-foreground leading-relaxed">{artist.full_biography}</p>
                </div>
              </div>
            </div>
          </div>
        </section>
      )}

      {/* Portfolio Gallery */}
      <section className="py-12 relative overflow-hidden">
        <FishAnimation />
        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Portfolio Gallery</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto mb-8">
              Explore the full collection of custom aquarium decor pieces, each uniquely crafted to create stunning underwater environments.
            </p>
            
            {/* Category Filter */}
            <div className="flex flex-wrap justify-center gap-2">
              {categories.map((category) => (
                <Button
                  key={category}
                  variant={selectedCategory === category ? "default" : "outline"}
                  onClick={() => setSelectedCategory(category)}
                  size="sm"
                >
                  {category}
                </Button>
              ))}
            </div>
          </div>
          
          {/* Portfolio Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredItems.map((item) => (
              <Card key={item.id} className="group overflow-hidden hover:shadow-lg transition-all duration-300 hover-scale">
                <div className="relative overflow-hidden">
                  <img 
                    src={item.image_url} 
                    alt={item.title}
                    className="w-full h-64 object-cover transition-transform duration-300 group-hover:scale-105"
                  />
                  <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                    <Button variant="secondary" size="sm">
                      View Larger
                    </Button>
                  </div>
                </div>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="text-lg font-semibold">{item.title}</h3>
                    <Badge variant="secondary">{item.category}</Badge>
                  </div>
                  <p className="text-muted-foreground">{item.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
          
          {filteredItems.length === 0 && (
            <div className="text-center py-12">
              <p className="text-muted-foreground">No items found in this category.</p>
            </div>
          )}
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-20 ocean-gradient text-white relative overflow-hidden">
        <FishAnimation />
        <div className="container mx-auto px-4 text-center relative z-10">
          <h2 className="text-3xl font-bold mb-4">Inspired by What You See?</h2>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            Ready to commission a custom piece or explore our ready-to-ship collection? Let's create something amazing together.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link to="/contact">
              <Button size="lg" variant="secondary">
                Commission Custom Work
              </Button>
            </Link>
            <Link to="/shop">
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white/10">
                Browse Shop
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Portfolio;