import { Routes, Route } from 'react-router-dom';
import ProtectedRoute from '@/components/ProtectedRoute';
import AdminLayout from '@/components/admin/AdminLayout';
import AdminDashboard from './AdminDashboard';
import HomepageManagement from './HomepageManagement';
import PortfolioManagement from './PortfolioManagement';
import OrderManagement from './OrderManagement';

const AdminRoutes = () => {
  return (
    <ProtectedRoute requireAdmin={true}>
      <AdminLayout>
        <Routes>
          <Route path="/" element={<AdminDashboard />} />
          <Route path="/homepage" element={<HomepageManagement />} />
          <Route path="/portfolio" element={<PortfolioManagement />} />
          <Route path="/products" element={<div>Product Management - Under Development</div>} />
          <Route path="/orders" element={<OrderManagement />} />
          <Route path="/customers" element={<div>Customer Management - Coming Soon</div>} />
          <Route path="/analytics" element={<div>Analytics - Coming Soon</div>} />
          <Route path="/contact" element={<div>Contact Forms - Coming Soon</div>} />
          <Route path="/settings" element={<div>Settings - Coming Soon</div>} />
        </Routes>
      </AdminLayout>
    </ProtectedRoute>
  );
};

export default AdminRoutes;
