import { Link } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  ShoppingCart, 
  Plus, 
  Minus, 
  Trash2, 
  ArrowLeft,
  CreditCard,
  Loader2
} from 'lucide-react';
import { useCart } from '@/hooks/useCart';
import { useAuth } from '@/hooks/useAuth';
import Navbar from '@/components/Navbar';

const Cart = () => {
  const { 
    items, 
    loading, 
    updateQuantity, 
    removeFromCart, 
    clearCart,
    getTotalItems,
    getSubtotal,
    getTaxAmount,
    getShippingAmount,
    getTotalPrice
  } = useCart();
  const { user } = useAuth();

  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <Navbar />
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center h-64">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <Navbar />
      <div className="container mx-auto px-4 py-8">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center space-x-4">
            <Link to="/shop">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Continue Shopping
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold">Shopping Cart</h1>
              <p className="text-muted-foreground">
                {getTotalItems()} {getTotalItems() === 1 ? 'item' : 'items'} in your cart
              </p>
            </div>
          </div>

          {items.length > 0 ? (
            <div className="grid gap-6 lg:grid-cols-3">
              {/* Cart Items */}
              <div className="lg:col-span-2 space-y-4">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between">
                    <CardTitle>Cart Items</CardTitle>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      onClick={clearCart}
                      className="text-destructive hover:text-destructive"
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Clear Cart
                    </Button>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {items.map((item) => (
                      <div key={item.id} className="flex items-center space-x-4 p-4 border rounded-lg">
                        <div className="w-20 h-20 bg-muted rounded-md flex items-center justify-center overflow-hidden">
                          <img 
                            src={item.product.image_url} 
                            alt={item.product.name}
                            className="w-full h-full object-cover"
                          />
                        </div>
                        
                        <div className="flex-1">
                          <h3 className="font-medium">{item.product.name}</h3>
                          <p className="text-sm text-muted-foreground">
                            {item.product.category}
                          </p>
                          <div className="flex items-center space-x-2 mt-1">
                            <span className="font-bold">${item.unit_price.toFixed(2)}</span>
                            {item.product.sale_price && item.product.sale_price !== item.product.price && (
                              <span className="text-sm text-muted-foreground line-through">
                                ${item.product.price.toFixed(2)}
                              </span>
                            )}
                          </div>
                        </div>

                        <div className="flex items-center space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => updateQuantity(item.id, item.quantity - 1)}
                            disabled={item.quantity <= 1}
                          >
                            <Minus className="h-4 w-4" />
                          </Button>
                          <Input
                            type="number"
                            value={item.quantity}
                            onChange={(e) => {
                              const newQuantity = parseInt(e.target.value) || 1;
                              updateQuantity(item.id, newQuantity);
                            }}
                            className="w-16 text-center"
                            min="1"
                            max={item.product.stock_quantity || 999}
                          />
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => updateQuantity(item.id, item.quantity + 1)}
                            disabled={
                              item.product.stock_quantity ? 
                              item.quantity >= item.product.stock_quantity : 
                              false
                            }
                          >
                            <Plus className="h-4 w-4" />
                          </Button>
                        </div>

                        <div className="text-right">
                          <div className="font-bold">
                            ${(item.unit_price * item.quantity).toFixed(2)}
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => removeFromCart(item.id)}
                            className="text-destructive hover:text-destructive"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </CardContent>
                </Card>
              </div>

              {/* Order Summary */}
              <div className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Order Summary</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span>Subtotal ({getTotalItems()} items)</span>
                        <span>${getSubtotal().toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Tax</span>
                        <span>${getTaxAmount().toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Shipping</span>
                        <span>
                          {getShippingAmount() === 0 ? (
                            <Badge variant="secondary">Free</Badge>
                          ) : (
                            `$${getShippingAmount().toFixed(2)}`
                          )}
                        </span>
                      </div>
                      {getSubtotal() < 100 && getSubtotal() > 0 && (
                        <p className="text-xs text-muted-foreground">
                          Add ${(100 - getSubtotal()).toFixed(2)} more for free shipping
                        </p>
                      )}
                    </div>
                    
                    <Separator />
                    
                    <div className="flex justify-between font-bold text-lg">
                      <span>Total</span>
                      <span>${getTotalPrice().toFixed(2)}</span>
                    </div>

                    <div className="space-y-2">
                      {user ? (
                        <Link to="/checkout" className="w-full">
                          <Button className="w-full" size="lg">
                            <CreditCard className="h-4 w-4 mr-2" />
                            Proceed to Checkout
                          </Button>
                        </Link>
                      ) : (
                        <div className="space-y-2">
                          <Link to="/login" className="w-full">
                            <Button className="w-full" size="lg">
                              Sign In to Checkout
                            </Button>
                          </Link>
                          <Link to="/register" className="w-full">
                            <Button variant="outline" className="w-full">
                              Create Account
                            </Button>
                          </Link>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>

                {/* Security Badge */}
                <Card>
                  <CardContent className="pt-6">
                    <div className="text-center space-y-2">
                      <div className="text-sm font-medium">Secure Checkout</div>
                      <div className="text-xs text-muted-foreground">
                        Your payment information is encrypted and secure
                      </div>
                      <div className="flex justify-center space-x-2">
                        <Badge variant="outline">SSL Encrypted</Badge>
                        <Badge variant="outline">Secure</Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          ) : (
            /* Empty Cart */
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <ShoppingCart className="h-16 w-16 text-muted-foreground mb-4" />
                <h3 className="text-xl font-medium mb-2">Your cart is empty</h3>
                <p className="text-muted-foreground mb-6 text-center">
                  Looks like you haven't added any items to your cart yet.
                  <br />
                  Start shopping to fill it up!
                </p>
                <Link to="/shop">
                  <Button size="lg">
                    Browse Products
                  </Button>
                </Link>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
};

export default Cart;
