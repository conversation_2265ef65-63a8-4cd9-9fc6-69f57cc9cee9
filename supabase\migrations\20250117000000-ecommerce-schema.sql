-- E-commerce Schema Extension for Depth Perception Aquascapes
-- This migration adds comprehensive e-commerce functionality

-- Create user roles enum
CREATE TYPE user_role AS ENUM ('customer', 'admin', 'super_admin');

-- Create order status enum
CREATE TYPE order_status AS ENUM ('pending', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded');

-- Create payment status enum
CREATE TYPE payment_status AS ENUM ('pending', 'completed', 'failed', 'refunded');

-- Extend existing products table with additional e-commerce fields
ALTER TABLE public.products ADD COLUMN IF NOT EXISTS sku TEXT UNIQUE;
ALTER TABLE public.products ADD COLUMN IF NOT EXISTS stock_quantity INTEGER DEFAULT 0;
ALTER TABLE public.products ADD COLUMN IF NOT EXISTS weight DECIMAL(8,2);
ALTER TABLE public.products ADD COLUMN IF NOT EXISTS dimensions TEXT;
ALTER TABLE public.products ADD COLUMN IF NOT EXISTS sale_price DECIMAL(10,2);
ALTER TABLE public.products ADD COLUMN IF NOT EXISTS is_digital BOOLEAN DEFAULT false;
ALTER TABLE public.products ADD COLUMN IF NOT EXISTS requires_shipping BOOLEAN DEFAULT true;
ALTER TABLE public.products ADD COLUMN IF NOT EXISTS meta_title TEXT;
ALTER TABLE public.products ADD COLUMN IF NOT EXISTS meta_description TEXT;
ALTER TABLE public.products ADD COLUMN IF NOT EXISTS status TEXT DEFAULT 'active';

-- Create product categories table
CREATE TABLE public.product_categories (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL UNIQUE,
  slug TEXT NOT NULL UNIQUE,
  description TEXT,
  parent_id UUID REFERENCES public.product_categories(id),
  sort_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create product attributes table (for materials, sizes, etc.)
CREATE TABLE public.product_attributes (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  slug TEXT NOT NULL,
  type TEXT DEFAULT 'text', -- text, select, multiselect, number, boolean
  is_required BOOLEAN DEFAULT false,
  sort_order INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create product attribute values table
CREATE TABLE public.product_attribute_values (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  attribute_id UUID NOT NULL REFERENCES public.product_attributes(id) ON DELETE CASCADE,
  value TEXT NOT NULL,
  sort_order INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create product-category relationship table
CREATE TABLE public.product_category_relations (
  product_id UUID NOT NULL REFERENCES public.products(id) ON DELETE CASCADE,
  category_id UUID NOT NULL REFERENCES public.product_categories(id) ON DELETE CASCADE,
  PRIMARY KEY (product_id, category_id)
);

-- Create product-attribute relationship table
CREATE TABLE public.product_attribute_relations (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  product_id UUID NOT NULL REFERENCES public.products(id) ON DELETE CASCADE,
  attribute_id UUID NOT NULL REFERENCES public.product_attributes(id) ON DELETE CASCADE,
  value TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create product images table
CREATE TABLE public.product_images (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  product_id UUID NOT NULL REFERENCES public.products(id) ON DELETE CASCADE,
  image_url TEXT NOT NULL,
  alt_text TEXT,
  is_primary BOOLEAN DEFAULT false,
  sort_order INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create user profiles table (extends Supabase auth.users)
CREATE TABLE public.user_profiles (
  id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
  role user_role DEFAULT 'customer',
  first_name TEXT,
  last_name TEXT,
  phone TEXT,
  date_of_birth DATE,
  avatar_url TEXT,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create user addresses table
CREATE TABLE public.user_addresses (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES public.user_profiles(id) ON DELETE CASCADE,
  type TEXT DEFAULT 'shipping', -- shipping, billing
  first_name TEXT NOT NULL,
  last_name TEXT NOT NULL,
  company TEXT,
  address_line_1 TEXT NOT NULL,
  address_line_2 TEXT,
  city TEXT NOT NULL,
  state TEXT NOT NULL,
  postal_code TEXT NOT NULL,
  country TEXT NOT NULL DEFAULT 'US',
  phone TEXT,
  is_default BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create shopping carts table
CREATE TABLE public.shopping_carts (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
  session_id TEXT, -- For guest users
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  CONSTRAINT cart_user_or_session CHECK (
    (user_id IS NOT NULL AND session_id IS NULL) OR 
    (user_id IS NULL AND session_id IS NOT NULL)
  )
);

-- Create cart items table
CREATE TABLE public.cart_items (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  cart_id UUID NOT NULL REFERENCES public.shopping_carts(id) ON DELETE CASCADE,
  product_id UUID NOT NULL REFERENCES public.products(id) ON DELETE CASCADE,
  quantity INTEGER NOT NULL DEFAULT 1 CHECK (quantity > 0),
  unit_price DECIMAL(10,2) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  UNIQUE(cart_id, product_id)
);

-- Create orders table
CREATE TABLE public.orders (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  order_number TEXT NOT NULL UNIQUE,
  user_id UUID REFERENCES public.user_profiles(id),
  status order_status DEFAULT 'pending',
  payment_status payment_status DEFAULT 'pending',
  subtotal DECIMAL(10,2) NOT NULL DEFAULT 0,
  tax_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
  shipping_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
  discount_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
  total_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
  currency TEXT DEFAULT 'USD',
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create order addresses table (billing and shipping)
CREATE TABLE public.order_addresses (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  order_id UUID NOT NULL REFERENCES public.orders(id) ON DELETE CASCADE,
  type TEXT NOT NULL, -- 'billing' or 'shipping'
  first_name TEXT NOT NULL,
  last_name TEXT NOT NULL,
  company TEXT,
  address_line_1 TEXT NOT NULL,
  address_line_2 TEXT,
  city TEXT NOT NULL,
  state TEXT NOT NULL,
  postal_code TEXT NOT NULL,
  country TEXT NOT NULL DEFAULT 'US',
  phone TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create order items table
CREATE TABLE public.order_items (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  order_id UUID NOT NULL REFERENCES public.orders(id) ON DELETE CASCADE,
  product_id UUID NOT NULL REFERENCES public.products(id),
  product_name TEXT NOT NULL, -- Store product name at time of order
  product_sku TEXT, -- Store SKU at time of order
  quantity INTEGER NOT NULL CHECK (quantity > 0),
  unit_price DECIMAL(10,2) NOT NULL,
  total_price DECIMAL(10,2) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create coupons table
CREATE TABLE public.coupons (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  code TEXT NOT NULL UNIQUE,
  description TEXT,
  discount_type TEXT NOT NULL, -- 'percentage' or 'fixed'
  discount_value DECIMAL(10,2) NOT NULL,
  minimum_amount DECIMAL(10,2),
  maximum_discount DECIMAL(10,2),
  usage_limit INTEGER,
  used_count INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  starts_at TIMESTAMP WITH TIME ZONE,
  expires_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create order coupons table (track coupon usage)
CREATE TABLE public.order_coupons (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  order_id UUID NOT NULL REFERENCES public.orders(id) ON DELETE CASCADE,
  coupon_id UUID NOT NULL REFERENCES public.coupons(id),
  coupon_code TEXT NOT NULL,
  discount_amount DECIMAL(10,2) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create payment transactions table
CREATE TABLE public.payment_transactions (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  order_id UUID NOT NULL REFERENCES public.orders(id) ON DELETE CASCADE,
  transaction_id TEXT NOT NULL,
  payment_method TEXT NOT NULL, -- 'stripe', 'paypal', etc.
  amount DECIMAL(10,2) NOT NULL,
  currency TEXT DEFAULT 'USD',
  status payment_status DEFAULT 'pending',
  gateway_response JSONB,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create order status history table
CREATE TABLE public.order_status_history (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  order_id UUID NOT NULL REFERENCES public.orders(id) ON DELETE CASCADE,
  status order_status NOT NULL,
  notes TEXT,
  created_by UUID REFERENCES public.user_profiles(id),
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create site settings table for admin configuration
CREATE TABLE public.site_settings (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  key TEXT NOT NULL UNIQUE,
  value JSONB,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create email templates table
CREATE TABLE public.email_templates (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL UNIQUE,
  subject TEXT NOT NULL,
  html_content TEXT NOT NULL,
  text_content TEXT,
  variables JSONB, -- Available template variables
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Add triggers for updated_at columns
CREATE TRIGGER update_product_categories_updated_at
BEFORE UPDATE ON public.product_categories
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_product_attributes_updated_at
BEFORE UPDATE ON public.product_attributes
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_user_profiles_updated_at
BEFORE UPDATE ON public.user_profiles
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_user_addresses_updated_at
BEFORE UPDATE ON public.user_addresses
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_shopping_carts_updated_at
BEFORE UPDATE ON public.shopping_carts
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_cart_items_updated_at
BEFORE UPDATE ON public.cart_items
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_orders_updated_at
BEFORE UPDATE ON public.orders
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_coupons_updated_at
BEFORE UPDATE ON public.coupons
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_payment_transactions_updated_at
BEFORE UPDATE ON public.payment_transactions
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_site_settings_updated_at
BEFORE UPDATE ON public.site_settings
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_email_templates_updated_at
BEFORE UPDATE ON public.email_templates
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

-- Enable Row Level Security on all new tables
ALTER TABLE public.product_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.product_attributes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.product_attribute_values ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.product_category_relations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.product_attribute_relations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.product_images ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_addresses ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.shopping_carts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.cart_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.order_addresses ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.order_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.coupons ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.order_coupons ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.payment_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.order_status_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.site_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.email_templates ENABLE ROW LEVEL SECURITY;

-- Create RLS policies

-- Product categories - public read, admin write
CREATE POLICY "Product categories are publicly readable"
ON public.product_categories FOR SELECT USING (true);

CREATE POLICY "Only admins can manage product categories"
ON public.product_categories FOR ALL
USING (EXISTS (
  SELECT 1 FROM public.user_profiles
  WHERE id = auth.uid() AND role IN ('admin', 'super_admin')
));

-- Product attributes - public read, admin write
CREATE POLICY "Product attributes are publicly readable"
ON public.product_attributes FOR SELECT USING (true);

CREATE POLICY "Only admins can manage product attributes"
ON public.product_attributes FOR ALL
USING (EXISTS (
  SELECT 1 FROM public.user_profiles
  WHERE id = auth.uid() AND role IN ('admin', 'super_admin')
));

-- Product attribute values - public read, admin write
CREATE POLICY "Product attribute values are publicly readable"
ON public.product_attribute_values FOR SELECT USING (true);

CREATE POLICY "Only admins can manage product attribute values"
ON public.product_attribute_values FOR ALL
USING (EXISTS (
  SELECT 1 FROM public.user_profiles
  WHERE id = auth.uid() AND role IN ('admin', 'super_admin')
));

-- Product relations - public read, admin write
CREATE POLICY "Product category relations are publicly readable"
ON public.product_category_relations FOR SELECT USING (true);

CREATE POLICY "Only admins can manage product category relations"
ON public.product_category_relations FOR ALL
USING (EXISTS (
  SELECT 1 FROM public.user_profiles
  WHERE id = auth.uid() AND role IN ('admin', 'super_admin')
));

CREATE POLICY "Product attribute relations are publicly readable"
ON public.product_attribute_relations FOR SELECT USING (true);

CREATE POLICY "Only admins can manage product attribute relations"
ON public.product_attribute_relations FOR ALL
USING (EXISTS (
  SELECT 1 FROM public.user_profiles
  WHERE id = auth.uid() AND role IN ('admin', 'super_admin')
));

-- Product images - public read, admin write
CREATE POLICY "Product images are publicly readable"
ON public.product_images FOR SELECT USING (true);

CREATE POLICY "Only admins can manage product images"
ON public.product_images FOR ALL
USING (EXISTS (
  SELECT 1 FROM public.user_profiles
  WHERE id = auth.uid() AND role IN ('admin', 'super_admin')
));

-- User profiles - users can read/update their own, admins can read all
CREATE POLICY "Users can view their own profile"
ON public.user_profiles FOR SELECT
USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile"
ON public.user_profiles FOR UPDATE
USING (auth.uid() = id);

CREATE POLICY "Admins can view all profiles"
ON public.user_profiles FOR SELECT
USING (EXISTS (
  SELECT 1 FROM public.user_profiles
  WHERE id = auth.uid() AND role IN ('admin', 'super_admin')
));

-- User addresses - users can manage their own
CREATE POLICY "Users can manage their own addresses"
ON public.user_addresses FOR ALL
USING (user_id = auth.uid());

CREATE POLICY "Admins can view all addresses"
ON public.user_addresses FOR SELECT
USING (EXISTS (
  SELECT 1 FROM public.user_profiles
  WHERE id = auth.uid() AND role IN ('admin', 'super_admin')
));

-- Shopping carts - users can manage their own
CREATE POLICY "Users can manage their own cart"
ON public.shopping_carts FOR ALL
USING (user_id = auth.uid() OR session_id IS NOT NULL);

-- Cart items - users can manage items in their cart
CREATE POLICY "Users can manage their own cart items"
ON public.cart_items FOR ALL
USING (EXISTS (
  SELECT 1 FROM public.shopping_carts
  WHERE id = cart_id AND (user_id = auth.uid() OR session_id IS NOT NULL)
));

-- Orders - users can view their own, admins can view all
CREATE POLICY "Users can view their own orders"
ON public.orders FOR SELECT
USING (user_id = auth.uid());

CREATE POLICY "Admins can manage all orders"
ON public.orders FOR ALL
USING (EXISTS (
  SELECT 1 FROM public.user_profiles
  WHERE id = auth.uid() AND role IN ('admin', 'super_admin')
));

-- Order addresses - users can view their own order addresses
CREATE POLICY "Users can view their own order addresses"
ON public.order_addresses FOR SELECT
USING (EXISTS (
  SELECT 1 FROM public.orders
  WHERE id = order_id AND user_id = auth.uid()
));

CREATE POLICY "Admins can view all order addresses"
ON public.order_addresses FOR SELECT
USING (EXISTS (
  SELECT 1 FROM public.user_profiles
  WHERE id = auth.uid() AND role IN ('admin', 'super_admin')
));

-- Order items - users can view their own order items
CREATE POLICY "Users can view their own order items"
ON public.order_items FOR SELECT
USING (EXISTS (
  SELECT 1 FROM public.orders
  WHERE id = order_id AND user_id = auth.uid()
));

CREATE POLICY "Admins can view all order items"
ON public.order_items FOR SELECT
USING (EXISTS (
  SELECT 1 FROM public.user_profiles
  WHERE id = auth.uid() AND role IN ('admin', 'super_admin')
));

-- Coupons - public read for active coupons, admin manage
CREATE POLICY "Active coupons are publicly readable"
ON public.coupons FOR SELECT
USING (is_active = true AND (starts_at IS NULL OR starts_at <= now()) AND (expires_at IS NULL OR expires_at > now()));

CREATE POLICY "Only admins can manage coupons"
ON public.coupons FOR ALL
USING (EXISTS (
  SELECT 1 FROM public.user_profiles
  WHERE id = auth.uid() AND role IN ('admin', 'super_admin')
));

-- Site settings - admin only
CREATE POLICY "Only admins can manage site settings"
ON public.site_settings FOR ALL
USING (EXISTS (
  SELECT 1 FROM public.user_profiles
  WHERE id = auth.uid() AND role IN ('admin', 'super_admin')
));

-- Email templates - admin only
CREATE POLICY "Only admins can manage email templates"
ON public.email_templates FOR ALL
USING (EXISTS (
  SELECT 1 FROM public.user_profiles
  WHERE id = auth.uid() AND role IN ('admin', 'super_admin')
));

-- Create function to generate order numbers
CREATE OR REPLACE FUNCTION public.generate_order_number()
RETURNS TEXT AS $$
DECLARE
  order_num TEXT;
  counter INTEGER;
BEGIN
  -- Get current date in YYYYMMDD format
  order_num := 'DP' || to_char(now(), 'YYYYMMDD');

  -- Get count of orders today
  SELECT COUNT(*) + 1 INTO counter
  FROM public.orders
  WHERE order_number LIKE order_num || '%';

  -- Append counter with leading zeros
  order_num := order_num || lpad(counter::TEXT, 4, '0');

  RETURN order_num;
END;
$$ LANGUAGE plpgsql;

-- Create function to update cart totals
CREATE OR REPLACE FUNCTION public.calculate_cart_total(cart_uuid UUID)
RETURNS DECIMAL(10,2) AS $$
DECLARE
  total DECIMAL(10,2);
BEGIN
  SELECT COALESCE(SUM(quantity * unit_price), 0) INTO total
  FROM public.cart_items
  WHERE cart_id = cart_uuid;

  RETURN total;
END;
$$ LANGUAGE plpgsql;

-- Create function to check product stock
CREATE OR REPLACE FUNCTION public.check_product_stock(product_uuid UUID, requested_quantity INTEGER)
RETURNS BOOLEAN AS $$
DECLARE
  current_stock INTEGER;
BEGIN
  SELECT stock_quantity INTO current_stock
  FROM public.products
  WHERE id = product_uuid;

  RETURN current_stock >= requested_quantity;
END;
$$ LANGUAGE plpgsql;

-- Insert sample product categories
INSERT INTO public.product_categories (name, slug, description, sort_order) VALUES
('Coral Decor', 'coral-decor', 'Beautiful coral replicas and reef decorations', 1),
('Caves & Hideouts', 'caves-hideouts', 'Cave systems and hiding places for fish', 2),
('Natural Elements', 'natural-elements', 'Driftwood, rocks, and natural-looking decorations', 3),
('Themed Decorations', 'themed-decorations', 'Pirate ships, castles, and fantasy themes', 4),
('Lighting & Features', 'lighting-features', 'LED lights, bubble walls, and special features', 5),
('Accessories', 'accessories', 'Small decorative items and accessories', 6);

-- Insert sample product attributes
INSERT INTO public.product_attributes (name, slug, type, is_required, sort_order) VALUES
('Material', 'material', 'select', true, 1),
('Size', 'size', 'select', true, 2),
('Color', 'color', 'select', false, 3),
('LED Lighting', 'led-lighting', 'boolean', false, 4),
('Weight', 'weight', 'number', false, 5);

-- Insert sample attribute values
INSERT INTO public.product_attribute_values (attribute_id, value, sort_order) VALUES
((SELECT id FROM public.product_attributes WHERE slug = 'material'), 'Resin', 1),
((SELECT id FROM public.product_attributes WHERE slug = 'material'), 'Ceramic', 2),
((SELECT id FROM public.product_attributes WHERE slug = 'material'), 'Treated Wood', 3),
((SELECT id FROM public.product_attributes WHERE slug = 'material'), 'Acrylic', 4),
((SELECT id FROM public.product_attributes WHERE slug = 'size'), 'Small (up to 6")', 1),
((SELECT id FROM public.product_attributes WHERE slug = 'size'), 'Medium (6-12")', 2),
((SELECT id FROM public.product_attributes WHERE slug = 'size'), 'Large (12-18")', 3),
((SELECT id FROM public.product_attributes WHERE slug = 'size'), 'Extra Large (18"+)', 4),
((SELECT id FROM public.product_attributes WHERE slug = 'color'), 'Natural', 1),
((SELECT id FROM public.product_attributes WHERE slug = 'color'), 'Colorful', 2),
((SELECT id FROM public.product_attributes WHERE slug = 'color'), 'Blue Tones', 3),
((SELECT id FROM public.product_attributes WHERE slug = 'color'), 'Green Tones', 4);

-- Update existing products with new fields
UPDATE public.products SET
  sku = 'CC-001',
  stock_quantity = 15,
  weight = 2.5,
  dimensions = '8" x 6" x 4"',
  requires_shipping = true,
  status = 'active'
WHERE name = 'Coral Castle Set';

UPDATE public.products SET
  sku = 'CS-002',
  stock_quantity = 8,
  weight = 5.2,
  dimensions = '12" x 10" x 8"',
  requires_shipping = true,
  status = 'active'
WHERE name = 'Cave System Deluxe';

UPDATE public.products SET
  sku = 'TC-003',
  stock_quantity = 25,
  weight = 0.8,
  dimensions = '4" x 3" x 3"',
  requires_shipping = true,
  status = 'active'
WHERE name = 'Treasure Chest with Coins';

-- Insert sample site settings
INSERT INTO public.site_settings (key, value, description) VALUES
('site_name', '"Depths of Perception"', 'Website name'),
('site_description', '"Custom Aquarium Decor from the Depths of Imagination"', 'Website description'),
('contact_email', '"<EMAIL>"', 'Contact email address'),
('shipping_rate', '9.99', 'Standard shipping rate'),
('tax_rate', '0.08', 'Tax rate (8%)'),
('currency', '"USD"', 'Default currency'),
('order_email_notifications', 'true', 'Send email notifications for orders');

-- Insert sample email templates
INSERT INTO public.email_templates (name, subject, html_content, text_content, variables) VALUES
('order_confirmation', 'Order Confirmation - {{order_number}}',
'<h1>Thank you for your order!</h1><p>Your order {{order_number}} has been received and is being processed.</p><p>Order Total: ${{total_amount}}</p>',
'Thank you for your order! Your order {{order_number}} has been received and is being processed. Order Total: ${{total_amount}}',
'{"order_number": "Order number", "total_amount": "Order total", "customer_name": "Customer name"}'),
('order_shipped', 'Your Order Has Shipped - {{order_number}}',
'<h1>Your order is on its way!</h1><p>Order {{order_number}} has been shipped and should arrive within 3-5 business days.</p>',
'Your order is on its way! Order {{order_number}} has been shipped and should arrive within 3-5 business days.',
'{"order_number": "Order number", "tracking_number": "Tracking number", "customer_name": "Customer name"}');
