import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Save, Eye, Star, Image as ImageIcon } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { Database } from '@/integrations/supabase/types';

type ArtistProfile = Database['public']['Tables']['artist_profile']['Row'];
type PortfolioItem = Database['public']['Tables']['portfolio_items']['Row'];
type Product = Database['public']['Tables']['products']['Row'];

const HomepageManagement = () => {
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [artistProfile, setArtistProfile] = useState<ArtistProfile | null>(null);
  const [featuredPortfolio, setFeaturedPortfolio] = useState<PortfolioItem[]>([]);
  const [featuredProducts, setFeaturedProducts] = useState<Product[]>([]);
  const [allPortfolio, setAllPortfolio] = useState<PortfolioItem[]>([]);
  const [allProducts, setAllProducts] = useState<Product[]>([]);
  const { toast } = useToast();

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      // Fetch artist profile
      const { data: artistData } = await supabase
        .from('artist_profile')
        .select('*')
        .single();
      
      if (artistData) setArtistProfile(artistData);

      // Fetch featured portfolio items
      const { data: featuredPortfolioData } = await supabase
        .from('portfolio_items')
        .select('*')
        .eq('featured', true)
        .order('sort_order');
      
      if (featuredPortfolioData) setFeaturedPortfolio(featuredPortfolioData);

      // Fetch all portfolio items
      const { data: allPortfolioData } = await supabase
        .from('portfolio_items')
        .select('*')
        .order('sort_order');
      
      if (allPortfolioData) setAllPortfolio(allPortfolioData);

      // Fetch featured products
      const { data: featuredProductsData } = await supabase
        .from('products')
        .select('*')
        .eq('featured', true)
        .order('sort_order');
      
      if (featuredProductsData) setFeaturedProducts(featuredProductsData);

      // Fetch all products
      const { data: allProductsData } = await supabase
        .from('products')
        .select('*')
        .order('sort_order');
      
      if (allProductsData) setAllProducts(allProductsData);

    } catch (error) {
      console.error('Error fetching data:', error);
      toast({
        title: "Error",
        description: "Failed to load homepage data",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const updateArtistProfile = async () => {
    if (!artistProfile) return;
    
    setSaving(true);
    try {
      const { error } = await supabase
        .from('artist_profile')
        .update({
          name: artistProfile.name,
          bio: artistProfile.bio,
          full_biography: artistProfile.full_biography,
          portrait_url: artistProfile.portrait_url,
          hero_image_url: artistProfile.hero_image_url,
        })
        .eq('id', artistProfile.id);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Artist profile updated successfully",
      });
    } catch (error) {
      console.error('Error updating artist profile:', error);
      toast({
        title: "Error",
        description: "Failed to update artist profile",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  const toggleFeaturedPortfolio = async (item: PortfolioItem) => {
    try {
      const { error } = await supabase
        .from('portfolio_items')
        .update({ featured: !item.featured })
        .eq('id', item.id);

      if (error) throw error;

      // Update local state
      setAllPortfolio(prev => 
        prev.map(p => p.id === item.id ? { ...p, featured: !p.featured } : p)
      );
      
      if (item.featured) {
        setFeaturedPortfolio(prev => prev.filter(p => p.id !== item.id));
      } else {
        setFeaturedPortfolio(prev => [...prev, { ...item, featured: true }]);
      }

      toast({
        title: "Success",
        description: `Portfolio item ${item.featured ? 'removed from' : 'added to'} featured`,
      });
    } catch (error) {
      console.error('Error toggling featured portfolio:', error);
      toast({
        title: "Error",
        description: "Failed to update featured status",
        variant: "destructive",
      });
    }
  };

  const toggleFeaturedProduct = async (product: Product) => {
    try {
      const { error } = await supabase
        .from('products')
        .update({ featured: !product.featured })
        .eq('id', product.id);

      if (error) throw error;

      // Update local state
      setAllProducts(prev => 
        prev.map(p => p.id === product.id ? { ...p, featured: !p.featured } : p)
      );
      
      if (product.featured) {
        setFeaturedProducts(prev => prev.filter(p => p.id !== product.id));
      } else {
        setFeaturedProducts(prev => [...prev, { ...product, featured: true }]);
      }

      toast({
        title: "Success",
        description: `Product ${product.featured ? 'removed from' : 'added to'} featured`,
      });
    } catch (error) {
      console.error('Error toggling featured product:', error);
      toast({
        title: "Error",
        description: "Failed to update featured status",
        variant: "destructive",
      });
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Homepage Management</h1>
        <p className="text-muted-foreground">
          Manage your homepage content, featured items, and artist profile
        </p>
      </div>

      <Tabs defaultValue="artist" className="space-y-4">
        <TabsList>
          <TabsTrigger value="artist">Artist Profile</TabsTrigger>
          <TabsTrigger value="portfolio">Featured Portfolio</TabsTrigger>
          <TabsTrigger value="products">Featured Products</TabsTrigger>
        </TabsList>

        <TabsContent value="artist" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Artist Profile</CardTitle>
              <CardDescription>
                Update the artist information displayed on the homepage
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {artistProfile && (
                <>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="name">Artist Name</Label>
                      <Input
                        id="name"
                        value={artistProfile.name}
                        onChange={(e) => setArtistProfile(prev => 
                          prev ? { ...prev, name: e.target.value } : null
                        )}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="portrait">Portrait URL</Label>
                      <Input
                        id="portrait"
                        value={artistProfile.portrait_url || ''}
                        onChange={(e) => setArtistProfile(prev => 
                          prev ? { ...prev, portrait_url: e.target.value } : null
                        )}
                        placeholder="https://example.com/portrait.jpg"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="hero">Hero Image URL</Label>
                    <Input
                      id="hero"
                      value={artistProfile.hero_image_url || ''}
                      onChange={(e) => setArtistProfile(prev => 
                        prev ? { ...prev, hero_image_url: e.target.value } : null
                      )}
                      placeholder="https://example.com/hero.jpg"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="bio">Short Bio</Label>
                    <Textarea
                      id="bio"
                      value={artistProfile.bio || ''}
                      onChange={(e) => setArtistProfile(prev => 
                        prev ? { ...prev, bio: e.target.value } : null
                      )}
                      placeholder="Brief description for the homepage"
                      rows={3}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="fullBio">Full Biography</Label>
                    <Textarea
                      id="fullBio"
                      value={artistProfile.full_biography || ''}
                      onChange={(e) => setArtistProfile(prev => 
                        prev ? { ...prev, full_biography: e.target.value } : null
                      )}
                      placeholder="Detailed biography"
                      rows={6}
                    />
                  </div>

                  <Button onClick={updateArtistProfile} disabled={saving}>
                    {saving ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save className="mr-2 h-4 w-4" />
                        Save Changes
                      </>
                    )}
                  </Button>
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="portfolio" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Featured Portfolio Items</CardTitle>
              <CardDescription>
                Select which portfolio items to feature on the homepage
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {allPortfolio.map((item) => (
                  <div key={item.id} className="border rounded-lg p-4 space-y-3">
                    <div className="aspect-video bg-muted rounded-md flex items-center justify-center">
                      {item.image_url ? (
                        <img 
                          src={item.image_url} 
                          alt={item.title}
                          className="w-full h-full object-cover rounded-md"
                        />
                      ) : (
                        <ImageIcon className="h-8 w-8 text-muted-foreground" />
                      )}
                    </div>
                    <div>
                      <h3 className="font-medium">{item.title}</h3>
                      <p className="text-sm text-muted-foreground">{item.category}</p>
                    </div>
                    <div className="flex items-center justify-between">
                      <Badge variant={item.featured ? "default" : "secondary"}>
                        {item.featured ? "Featured" : "Not Featured"}
                      </Badge>
                      <Switch
                        checked={item.featured || false}
                        onCheckedChange={() => toggleFeaturedPortfolio(item)}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="products" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Featured Products</CardTitle>
              <CardDescription>
                Select which products to feature on the homepage
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {allProducts.map((product) => (
                  <div key={product.id} className="border rounded-lg p-4 space-y-3">
                    <div className="aspect-video bg-muted rounded-md flex items-center justify-center">
                      {product.image_url ? (
                        <img 
                          src={product.image_url} 
                          alt={product.name}
                          className="w-full h-full object-cover rounded-md"
                        />
                      ) : (
                        <ImageIcon className="h-8 w-8 text-muted-foreground" />
                      )}
                    </div>
                    <div>
                      <h3 className="font-medium">{product.name}</h3>
                      <p className="text-sm text-muted-foreground">${product.price}</p>
                    </div>
                    <div className="flex items-center justify-between">
                      <Badge variant={product.featured ? "default" : "secondary"}>
                        {product.featured ? "Featured" : "Not Featured"}
                      </Badge>
                      <Switch
                        checked={product.featured || false}
                        onCheckedChange={() => toggleFeaturedProduct(product)}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default HomepageManagement;
