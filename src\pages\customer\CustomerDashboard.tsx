import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { 
  Package, 
  User, 
  ShoppingCart, 
  Clock, 
  CheckCircle, 
  Eye,
  Edit
} from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { Database } from '@/integrations/supabase/types';

type Order = Database['public']['Tables']['orders']['Row'];
type OrderStatus = Database['public']['Enums']['order_status'];
type PaymentStatus = Database['public']['Enums']['payment_status'];

const CustomerDashboard = () => {
  const { user, profile } = useAuth();
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchCustomerOrders();
  }, [user]);

  const fetchCustomerOrders = async () => {
    try {
      // Mock data for demonstration - in real implementation, fetch from database
      const mockOrders: Order[] = [
        {
          id: '1',
          order_number: 'DP20250117001',
          user_id: user?.id || null,
          status: 'delivered',
          payment_status: 'completed',
          subtotal: 89.99,
          tax_amount: 7.20,
          shipping_amount: 9.99,
          discount_amount: 0,
          total_amount: 107.18,
          currency: 'USD',
          notes: null,
          created_at: new Date(Date.now() - 7 * 86400000).toISOString(), // 7 days ago
          updated_at: new Date(Date.now() - 2 * 86400000).toISOString(), // 2 days ago
        },
        {
          id: '2',
          order_number: 'DP20250117002',
          user_id: user?.id || null,
          status: 'processing',
          payment_status: 'completed',
          subtotal: 129.99,
          tax_amount: 10.40,
          shipping_amount: 9.99,
          discount_amount: 10.00,
          total_amount: 140.38,
          currency: 'USD',
          notes: 'Rush order',
          created_at: new Date(Date.now() - 2 * 86400000).toISOString(), // 2 days ago
          updated_at: new Date().toISOString(),
        },
        {
          id: '3',
          order_number: 'DP20250117003',
          user_id: user?.id || null,
          status: 'pending',
          payment_status: 'pending',
          subtotal: 45.99,
          tax_amount: 3.68,
          shipping_amount: 9.99,
          discount_amount: 5.00,
          total_amount: 54.66,
          currency: 'USD',
          notes: null,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
      ];

      setOrders(mockOrders);
    } catch (error) {
      console.error('Error fetching orders:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadge = (status: OrderStatus) => {
    const statusConfig = {
      pending: { variant: 'secondary' as const, icon: Clock, color: 'text-yellow-600' },
      processing: { variant: 'default' as const, icon: Package, color: 'text-blue-600' },
      shipped: { variant: 'default' as const, icon: Package, color: 'text-purple-600' },
      delivered: { variant: 'default' as const, icon: CheckCircle, color: 'text-green-600' },
      cancelled: { variant: 'destructive' as const, icon: CheckCircle, color: 'text-red-600' },
      refunded: { variant: 'destructive' as const, icon: CheckCircle, color: 'text-red-600' },
    };

    const config = statusConfig[status];
    const Icon = config.icon;

    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className="h-3 w-3" />
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const getPaymentStatusBadge = (status: PaymentStatus) => {
    const statusConfig = {
      pending: { variant: 'secondary' as const },
      completed: { variant: 'default' as const },
      failed: { variant: 'destructive' as const },
      refunded: { variant: 'destructive' as const },
    };

    const config = statusConfig[status];

    return (
      <Badge variant={config.variant}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const recentOrders = orders.slice(0, 3);
  const totalSpent = orders
    .filter(order => order.payment_status === 'completed')
    .reduce((sum, order) => sum + order.total_amount, 0);

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        <div className="space-y-6">
          {/* Header */}
          <div>
            <h1 className="text-3xl font-bold">My Dashboard</h1>
            <p className="text-muted-foreground">
              Welcome back, {profile?.first_name || user?.email}!
            </p>
          </div>

          {/* Stats Cards */}
          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Orders</CardTitle>
                <ShoppingCart className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{orders.length}</div>
                <p className="text-xs text-muted-foreground">
                  {orders.filter(o => o.status === 'pending').length} pending
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Spent</CardTitle>
                <Package className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">${totalSpent.toFixed(2)}</div>
                <p className="text-xs text-muted-foreground">
                  Across {orders.filter(o => o.payment_status === 'completed').length} completed orders
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Account Status</CardTitle>
                <User className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">Active</div>
                <p className="text-xs text-muted-foreground">
                  Member since {new Date(user?.created_at || '').toLocaleDateString()}
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Main Content */}
          <Tabs defaultValue="orders" className="space-y-4">
            <TabsList>
              <TabsTrigger value="orders">Recent Orders</TabsTrigger>
              <TabsTrigger value="profile">Profile</TabsTrigger>
            </TabsList>

            <TabsContent value="orders" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Recent Orders</CardTitle>
                  <CardDescription>
                    Your most recent purchases and their status
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {loading ? (
                    <div className="text-center py-8">Loading orders...</div>
                  ) : recentOrders.length > 0 ? (
                    <div className="space-y-4">
                      {recentOrders.map((order) => (
                        <div key={order.id} className="border rounded-lg p-4">
                          <div className="flex justify-between items-start mb-3">
                            <div>
                              <h3 className="font-medium">{order.order_number}</h3>
                              <p className="text-sm text-muted-foreground">
                                Ordered on {new Date(order.created_at).toLocaleDateString()}
                              </p>
                            </div>
                            <div className="text-right">
                              <div className="font-bold">${order.total_amount.toFixed(2)}</div>
                              <div className="flex gap-2 mt-1">
                                {getStatusBadge(order.status)}
                                {getPaymentStatusBadge(order.payment_status)}
                              </div>
                            </div>
                          </div>
                          
                          <div className="flex justify-between items-center">
                            <div className="text-sm text-muted-foreground">
                              Last updated: {new Date(order.updated_at).toLocaleDateString()}
                            </div>
                            <Button variant="outline" size="sm">
                              <Eye className="h-4 w-4 mr-2" />
                              View Details
                            </Button>
                          </div>
                        </div>
                      ))}
                      
                      {orders.length > 3 && (
                        <div className="text-center pt-4">
                          <Button variant="outline">
                            View All Orders ({orders.length})
                          </Button>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <ShoppingCart className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                      <h3 className="text-lg font-medium mb-2">No orders yet</h3>
                      <p className="text-muted-foreground mb-4">
                        Start shopping to see your orders here
                      </p>
                      <Button>
                        Browse Products
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="profile" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Profile Information</CardTitle>
                  <CardDescription>
                    Manage your account details and preferences
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium">First Name</label>
                      <p className="mt-1 text-sm text-muted-foreground">
                        {profile?.first_name || 'Not provided'}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium">Last Name</label>
                      <p className="mt-1 text-sm text-muted-foreground">
                        {profile?.last_name || 'Not provided'}
                      </p>
                    </div>
                  </div>

                  <div>
                    <label className="text-sm font-medium">Email Address</label>
                    <p className="mt-1 text-sm text-muted-foreground">
                      {user?.email}
                    </p>
                  </div>

                  <div>
                    <label className="text-sm font-medium">Phone Number</label>
                    <p className="mt-1 text-sm text-muted-foreground">
                      {profile?.phone || 'Not provided'}
                    </p>
                  </div>

                  <div>
                    <label className="text-sm font-medium">Account Type</label>
                    <p className="mt-1 text-sm text-muted-foreground capitalize">
                      {profile?.role || 'Customer'}
                    </p>
                  </div>

                  <div className="pt-4">
                    <Button>
                      <Edit className="h-4 w-4 mr-2" />
                      Edit Profile
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
};

export default CustomerDashboard;
