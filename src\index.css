@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    /* Ocean-inspired color palette */
    --background: 196 100% 98%;
    --foreground: 198 29% 16%;

    --card: 196 100% 99%;
    --card-foreground: 198 29% 16%;

    --popover: 196 100% 99%;
    --popover-foreground: 198 29% 16%;

    --primary: 200 85% 32%;
    --primary-foreground: 196 100% 98%;

    --secondary: 193 43% 88%;
    --secondary-foreground: 198 29% 16%;

    --muted: 193 43% 92%;
    --muted-foreground: 198 20% 45%;

    --accent: 182 70% 75%;
    --accent-foreground: 198 29% 16%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 193 43% 88%;
    --input: 193 43% 88%;
    --ring: 200 85% 32%;

    --radius: 0.75rem;

    /* Ocean gradients */
    --gradient-ocean: linear-gradient(135deg, hsl(200 85% 32%), hsl(193 70% 55%));
    --gradient-wave: linear-gradient(180deg, hsl(193 70% 75% / 0.2), hsl(200 85% 32% / 0.1));
    --gradient-deep: linear-gradient(to bottom, hsl(200 85% 32%), hsl(220 60% 25%));
    
    /* Shadows with ocean tint */
    --shadow-ocean: 0 10px 30px -10px hsl(200 85% 32% / 0.3);
    --shadow-gentle: 0 4px 16px -4px hsl(193 43% 50% / 0.2);

    --sidebar-background: 196 100% 98%;
    --sidebar-foreground: 198 29% 16%;
    --sidebar-primary: 200 85% 32%;
    --sidebar-primary-foreground: 196 100% 98%;
    --sidebar-accent: 193 43% 92%;
    --sidebar-accent-foreground: 198 29% 16%;
    --sidebar-border: 193 43% 88%;
    --sidebar-ring: 200 85% 32%;
  }

  .dark {
    /* Deep ocean dark theme */
    --background: 220 50% 8%;
    --foreground: 193 43% 88%;

    --card: 220 45% 10%;
    --card-foreground: 193 43% 88%;

    --popover: 220 45% 10%;
    --popover-foreground: 193 43% 88%;

    --primary: 182 70% 75%;
    --primary-foreground: 220 50% 8%;

    --secondary: 220 45% 15%;
    --secondary-foreground: 193 43% 88%;

    --muted: 220 45% 15%;
    --muted-foreground: 193 20% 65%;

    --accent: 200 85% 45%;
    --accent-foreground: 193 43% 88%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 220 45% 15%;
    --input: 220 45% 15%;
    --ring: 182 70% 75%;
    
    /* Dark theme gradients */
    --gradient-ocean: linear-gradient(135deg, hsl(220 50% 8%), hsl(200 85% 25%));
    --gradient-wave: linear-gradient(180deg, hsl(182 70% 75% / 0.1), hsl(200 85% 32% / 0.05));
    --gradient-deep: linear-gradient(to bottom, hsl(220 50% 8%), hsl(220 60% 15%));
    
    --shadow-ocean: 0 10px 30px -10px hsl(182 70% 75% / 0.2);
    --shadow-gentle: 0 4px 16px -4px hsl(200 85% 32% / 0.3);

    --sidebar-background: 220 50% 8%;
    --sidebar-foreground: 193 43% 88%;
    --sidebar-primary: 182 70% 75%;
    --sidebar-primary-foreground: 220 50% 8%;
    --sidebar-accent: 220 45% 15%;
    --sidebar-accent-foreground: 193 43% 88%;
    --sidebar-border: 220 45% 15%;
    --sidebar-ring: 182 70% 75%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* Fish swimming animation */
@keyframes swim {
  0% {
    transform: translateX(-100vw) translateY(0px);
  }
  25% {
    transform: translateX(25vw) translateY(-20px);
  }
  50% {
    transform: translateX(50vw) translateY(10px);
  }
  75% {
    transform: translateX(75vw) translateY(-15px);
  }
  100% {
    transform: translateX(120vw) translateY(5px);
  }
}

@keyframes swimSlow {
  0% {
    transform: translateX(-50px) translateY(0px);
  }
  25% {
    transform: translateX(25vw) translateY(15px);
  }
  50% {
    transform: translateX(50vw) translateY(-10px);
  }
  75% {
    transform: translateX(75vw) translateY(20px);
  }
  100% {
    transform: translateX(110vw) translateY(-5px);
  }
}

.fish-swim {
  animation: swim 20s linear infinite;
}

.fish-swim-slow {
  animation: swimSlow 35s linear infinite;
}

.ocean-gradient {
  background: var(--gradient-ocean);
}

.wave-overlay {
  background: var(--gradient-wave);
}