import { supabase } from '@/integrations/supabase/client';

/**
 * Creates a default admin user for development/testing
 * This should only be used in development environments
 */
export const createDefaultAdminUser = async () => {
  const adminEmail = '<EMAIL>';
  const adminPassword = 'admin123456';

  try {
    console.log('Starting admin user creation...');

    // Check if admin user already exists
    const { data: existingUser, error: checkError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('role', 'super_admin')
      .single();

    if (checkError && checkError.code !== 'PGRST116') {
      // PGRST116 is "not found" error, which is expected if no admin exists
      console.error('Error checking for existing admin:', checkError);
    }

    if (existingUser) {
      console.log('Admin user already exists');
      return {
        success: true,
        message: 'Admin user already exists',
        email: adminEmail
      };
    }

    console.log('No existing admin found, creating new admin user...');

    // Create the admin user
    console.log('Creating auth user...');
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email: adminEmail,
      password: adminPassword,
      options: {
        data: {
          first_name: 'Admin',
          last_name: 'User'
        }
      }
    });

    if (authError) {
      console.error('Auth error:', authError);
      throw authError;
    }

    console.log('Auth user created:', authData.user?.id);

    if (authData.user) {
      console.log('Creating user profile...');

      // First, try to insert a new user profile (in case it doesn't exist)
      const { error: insertError } = await supabase
        .from('user_profiles')
        .insert({
          id: authData.user.id,
          role: 'super_admin',
          first_name: 'Admin',
          last_name: 'User'
        });

      // If insert fails (user already exists), try to update
      if (insertError) {
        console.log('Insert failed, trying update:', insertError);
        const { error: updateError } = await supabase
          .from('user_profiles')
          .update({
            role: 'super_admin',
            first_name: 'Admin',
            last_name: 'User'
          })
          .eq('id', authData.user.id);

        if (updateError) {
          console.error('Update error:', updateError);
          throw updateError;
        }
      }

      console.log('User profile created/updated successfully');

      return {
        success: true,
        message: 'Admin user created successfully',
        email: adminEmail,
        password: adminPassword,
        userId: authData.user.id
      };
    }

    throw new Error('Failed to create user');

  } catch (error) {
    console.error('Error creating admin user:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};

// Helper function to promote existing user to admin
export const promoteUserToAdmin = async (userEmail: string) => {
  try {
    // Since we can't use admin functions on client side,
    // we'll try to get the current user and check if they're trying to promote themselves
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      throw new Error('No user is currently logged in. Please log in first.');
    }

    if (user.email !== userEmail) {
      throw new Error('You can only promote your own account. Please log in with the email you want to promote.');
    }

    // Update current user's profile to admin
    const { error: updateError } = await supabase
      .from('user_profiles')
      .update({ role: 'super_admin' })
      .eq('id', user.id);

    if (updateError) {
      throw updateError;
    }

    return {
      success: true,
      message: `User ${userEmail} promoted to admin`,
      userId: user.id
    };

  } catch (error) {
    console.error('Error promoting user:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};
