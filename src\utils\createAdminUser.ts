import { supabase } from '@/integrations/supabase/client';

export const createDefaultAdminUser = async () => {
  const adminEmail = '<EMAIL>';
  const adminPassword = 'admin123456';

  try {
    console.log('Creating admin user...');
    
    // Try to sign up the user
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email: adminEmail,
      password: adminPassword,
      options: {
        data: {
          first_name: 'Admin',
          last_name: 'User'
        }
      }
    });

    console.log('Auth signup result:', { authData, authError });

    // Handle case where user already exists
    let userId = authData?.user?.id;
    
    if (authError && authError.message.includes('already registered')) {
      console.log('User already exists, trying to sign in...');
      const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
        email: adminEmail,
        password: adminPassword
      });

      if (signInError) {
        throw new Error(`Cannot sign in: ${signInError.message}`);
      }

      userId = signInData?.user?.id;
    } else if (authError) {
      throw authError;
    }

    if (!userId) {
      throw new Error('Could not get user ID');
    }

    console.log('User ID:', userId);

    // Create or update the user profile with admin role
    const { data: profileData, error: profileError } = await supabase
      .from('user_profiles')
      .upsert({
        id: userId,
        email: adminEmail,
        first_name: 'Admin',
        last_name: 'User',
        role: 'super_admin'
      })
      .select()
      .single();

    console.log('Profile result:', { profileData, profileError });

    if (profileError) {
      throw profileError;
    }

    return {
      success: true,
      message: 'Admin user created successfully!',
      email: adminEmail,
      password: adminPassword
    };

  } catch (error: any) {
    console.error('Error creating admin user:', error);
    return {
      success: false,
      error: error.message || 'Unknown error occurred'
    };
  }
};

export const promoteUserToAdmin = async (email: string) => {
  try {
    console.log('Promoting user to admin:', email);
    
    // Find the user profile
    const { data: profile, error: findError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('email', email)
      .single();

    if (findError) {
      throw new Error(`User not found: ${findError.message}`);
    }

    // Update the user role to admin
    const { data: updatedProfile, error: updateError } = await supabase
      .from('user_profiles')
      .update({ role: 'super_admin' })
      .eq('id', profile.id)
      .select()
      .single();

    if (updateError) {
      throw updateError;
    }

    return {
      success: true,
      message: `User ${email} promoted to admin successfully!`,
      profile: updatedProfile
    };

  } catch (error: any) {
    console.error('Error promoting user:', error);
    return {
      success: false,
      error: error.message || 'Unknown error occurred'
    };
  }
};
