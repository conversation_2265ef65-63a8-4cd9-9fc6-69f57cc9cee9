import { supabase } from '@/integrations/supabase/client';

/**
 * Creates a default admin user for development/testing
 * This should only be used in development environments
 */
export const createDefaultAdminUser = async () => {
  const adminEmail = '<EMAIL>';
  const adminPassword = 'admin123456';

  try {
    // Check if admin user already exists
    const { data: existingUser } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('role', 'super_admin')
      .single();

    if (existingUser) {
      console.log('Admin user already exists');
      return {
        success: true,
        message: 'Admin user already exists',
        email: adminEmail
      };
    }

    // Create the admin user
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email: adminEmail,
      password: adminPassword,
      options: {
        data: {
          first_name: 'Admin',
          last_name: 'User'
        }
      }
    });

    if (authError) {
      throw authError;
    }

    if (authData.user) {
      // Update the user profile to admin role
      const { error: profileError } = await supabase
        .from('user_profiles')
        .update({
          role: 'super_admin',
          first_name: 'Admin',
          last_name: 'User'
        })
        .eq('id', authData.user.id);

      if (profileError) {
        throw profileError;
      }

      return {
        success: true,
        message: 'Admin user created successfully',
        email: adminEmail,
        password: adminPassword,
        userId: authData.user.id
      };
    }

    throw new Error('Failed to create user');

  } catch (error) {
    console.error('Error creating admin user:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};

// Helper function to promote existing user to admin
export const promoteUserToAdmin = async (userEmail: string) => {
  try {
    // Find user by email
    const { data: authUser, error: authError } = await supabase.auth.admin.getUserByEmail(userEmail);
    
    if (authError || !authUser) {
      throw new Error('User not found');
    }

    // Update user profile to admin
    const { error: updateError } = await supabase
      .from('user_profiles')
      .update({ role: 'super_admin' })
      .eq('id', authUser.user.id);

    if (updateError) {
      throw updateError;
    }

    return {
      success: true,
      message: `User ${userEmail} promoted to admin`,
      userId: authUser.user.id
    };

  } catch (error) {
    console.error('Error promoting user:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};
