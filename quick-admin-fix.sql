-- Quick Admin Fix Script
-- Run this in Supabase SQL Editor to create admin user

-- First, check if admin user exists in auth.users
SELECT 'Checking for existing admin user...' as status;
SELECT id, email, created_at FROM auth.users WHERE email = '<EMAIL>';

-- Check if admin profile exists
SELECT 'Checking for existing admin profile...' as status;
SELECT id, email, role FROM public.user_profiles WHERE email = '<EMAIL>';

-- If admin user exists but no profile, create profile
-- Replace 'USER_ID_HERE' with the actual user ID from the first query
INSERT INTO public.user_profiles (
  id,
  email,
  first_name,
  last_name,
  role,
  created_at,
  updated_at
)
SELECT 
  au.id,
  '<EMAIL>',
  'Admin',
  'User',
  'super_admin'::user_role,
  NOW(),
  NOW()
FROM auth.users au
WHERE au.email = '<EMAIL>'
AND NOT EXISTS (
  SELECT 1 FROM public.user_profiles up 
  WHERE up.id = au.id
);

-- Verify the admin user is set up correctly
SELECT 'Final verification...' as status;
SELECT 
  au.id,
  au.email,
  up.role,
  up.first_name,
  up.last_name
FROM auth.users au
JOIN public.user_profiles up ON au.id = up.id
WHERE au.email = '<EMAIL>';
