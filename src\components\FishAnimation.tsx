import { Fish } from "lucide-react";

const FishAnimation = () => {
  // Generate random fish with different sizes, colors, and positions
  const generateRandomFish = () => {
    const sizes = [4, 5, 6, 7, 8, 9, 10];
    const colors = ['text-primary', 'text-accent', 'text-blue-400', 'text-teal-400', 'text-cyan-400', 'text-indigo-400'];
    const opacities = ['opacity-15', 'opacity-20', 'opacity-25', 'opacity-30'];
    const speeds = ['fish-swim', 'fish-swim-slow'];
    
    return Array.from({ length: 12 }, (_, i) => ({
      id: i,
      size: sizes[Math.floor(Math.random() * sizes.length)],
      color: colors[Math.floor(Math.random() * colors.length)],
      opacity: opacities[Math.floor(Math.random() * opacities.length)],
      speed: speeds[Math.floor(Math.random() * speeds.length)],
      top: Math.random() * 80 + 10, // Random position between 10% and 90%
      delay: Math.random() * 30, // Random delay up to 30 seconds
    }));
  };

  const fishArray = generateRandomFish();

  return (
    <div className="fixed inset-0 overflow-hidden pointer-events-none z-0">
      {fishArray.map((fish) => (
        <div 
          key={fish.id}
          className={`${fish.speed} absolute ${fish.opacity}`}
          style={{ 
            top: `${fish.top}%`,
            animationDelay: `-${fish.delay}s`
          }}
        >
          <Fish className={`h-${fish.size} w-${fish.size} ${fish.color}`} />
        </div>
      ))}
    </div>
  );
};

export default FishAnimation;