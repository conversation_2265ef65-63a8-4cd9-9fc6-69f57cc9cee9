-- Manual Admin User Creation Script
-- Run this in your Supabase SQL Editor if the admin setup page doesn't work

-- First, let's check if there are any existing users
SELECT 
  au.id,
  au.email,
  au.created_at,
  up.role,
  up.first_name,
  up.last_name
FROM auth.users au
LEFT JOIN public.user_profiles up ON au.id = up.id
ORDER BY au.created_at DESC;

-- If you see your user in the results above, copy their ID and use it in the next query
-- Replace 'YOUR_USER_ID_HERE' with the actual UUID from the query above

-- Example: If your user ID is 'fe4b9a70-1bbc-461a-96fb-ec1b09746d08', use:
-- INSERT INTO public.user_profiles (id, role, first_name, last_name)
-- VALUES ('fe4b9a70-1bbc-461a-96fb-ec1b09746d08', 'super_admin', 'Admin', 'User')
-- ON CONFLICT (id) DO UPDATE SET role = 'super_admin';

-- STEP 1: Replace 'YOUR_USER_ID_HERE' with your actual user ID from the query above
-- STEP 2: Uncomment and run the following line:

-- INSERT INTO public.user_profiles (id, role, first_name, last_name)
-- VALUES ('YOUR_USER_ID_HERE', 'super_admin', 'Admin', 'User')
-- ON CONFLICT (id) DO UPDATE SET role = 'super_admin';

-- Alternative: If you want to create a completely new admin user, uncomment and run this:
-- This creates a user directly in the auth.users table (advanced method)

-- INSERT INTO auth.users (
--   instance_id,
--   id,
--   aud,
--   role,
--   email,
--   encrypted_password,
--   email_confirmed_at,
--   created_at,
--   updated_at,
--   confirmation_token,
--   email_change,
--   email_change_token_new,
--   recovery_token
-- ) VALUES (
--   '00000000-0000-0000-0000-000000000000',
--   gen_random_uuid(),
--   'authenticated',
--   'authenticated',
--   '<EMAIL>',
--   crypt('admin123456', gen_salt('bf')),
--   now(),
--   now(),
--   now(),
--   '',
--   '',
--   '',
--   ''
-- );

-- Then create the profile for the new user:
-- INSERT INTO public.user_profiles (id, role, first_name, last_name)
-- SELECT id, 'super_admin', 'Admin', 'User'
-- FROM auth.users 
-- WHERE email = '<EMAIL>';

-- Verify the admin user was created:
SELECT 
  au.id,
  au.email,
  up.role,
  up.first_name,
  up.last_name
FROM auth.users au
JOIN public.user_profiles up ON au.id = up.id
WHERE up.role = 'super_admin';
