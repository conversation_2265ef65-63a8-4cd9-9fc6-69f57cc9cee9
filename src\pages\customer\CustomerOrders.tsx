import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { 
  ArrowLeft,
  Search, 
  Eye, 
  Package, 
  Clock, 
  CheckCircle, 
  ShoppingCart,
  Download
} from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { Database } from '@/integrations/supabase/types';

type Order = Database['public']['Tables']['orders']['Row'];
type OrderStatus = Database['public']['Enums']['order_status'];
type PaymentStatus = Database['public']['Enums']['payment_status'];

const CustomerOrders = () => {
  const { user } = useAuth();
  const [orders, setOrders] = useState<Order[]>([]);
  const [filteredOrders, setFilteredOrders] = useState<Order[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [isDetailDialogOpen, setIsDetailDialogOpen] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchCustomerOrders();
  }, [user]);

  useEffect(() => {
    filterOrders();
  }, [orders, searchTerm]);

  const fetchCustomerOrders = async () => {
    try {
      // Mock data for demonstration - in real implementation, fetch from database
      const mockOrders: Order[] = [
        {
          id: '1',
          order_number: 'DP20250117001',
          user_id: user?.id || null,
          status: 'delivered',
          payment_status: 'completed',
          subtotal: 89.99,
          tax_amount: 7.20,
          shipping_amount: 9.99,
          discount_amount: 0,
          total_amount: 107.18,
          currency: 'USD',
          notes: null,
          created_at: new Date(Date.now() - 7 * 86400000).toISOString(),
          updated_at: new Date(Date.now() - 2 * 86400000).toISOString(),
        },
        {
          id: '2',
          order_number: 'DP20250117002',
          user_id: user?.id || null,
          status: 'processing',
          payment_status: 'completed',
          subtotal: 129.99,
          tax_amount: 10.40,
          shipping_amount: 9.99,
          discount_amount: 10.00,
          total_amount: 140.38,
          currency: 'USD',
          notes: 'Rush order',
          created_at: new Date(Date.now() - 2 * 86400000).toISOString(),
          updated_at: new Date().toISOString(),
        },
        {
          id: '3',
          order_number: 'DP20250117003',
          user_id: user?.id || null,
          status: 'pending',
          payment_status: 'pending',
          subtotal: 45.99,
          tax_amount: 3.68,
          shipping_amount: 9.99,
          discount_amount: 5.00,
          total_amount: 54.66,
          currency: 'USD',
          notes: null,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
        {
          id: '4',
          order_number: 'DP20250116001',
          user_id: user?.id || null,
          status: 'delivered',
          payment_status: 'completed',
          subtotal: 67.99,
          tax_amount: 5.44,
          shipping_amount: 9.99,
          discount_amount: 0,
          total_amount: 83.42,
          currency: 'USD',
          notes: null,
          created_at: new Date(Date.now() - 14 * 86400000).toISOString(),
          updated_at: new Date(Date.now() - 10 * 86400000).toISOString(),
        },
        {
          id: '5',
          order_number: 'DP20250115001',
          user_id: user?.id || null,
          status: 'cancelled',
          payment_status: 'refunded',
          subtotal: 156.99,
          tax_amount: 12.56,
          shipping_amount: 9.99,
          discount_amount: 15.00,
          total_amount: 164.54,
          currency: 'USD',
          notes: 'Cancelled by customer',
          created_at: new Date(Date.now() - 21 * 86400000).toISOString(),
          updated_at: new Date(Date.now() - 18 * 86400000).toISOString(),
        },
      ];

      setOrders(mockOrders);
    } catch (error) {
      console.error('Error fetching orders:', error);
    } finally {
      setLoading(false);
    }
  };

  const filterOrders = () => {
    let filtered = orders;

    if (searchTerm) {
      filtered = filtered.filter(order => 
        order.order_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.notes?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    setFilteredOrders(filtered);
  };

  const getStatusBadge = (status: OrderStatus) => {
    const statusConfig = {
      pending: { variant: 'secondary' as const, icon: Clock },
      processing: { variant: 'default' as const, icon: Package },
      shipped: { variant: 'default' as const, icon: Package },
      delivered: { variant: 'default' as const, icon: CheckCircle },
      cancelled: { variant: 'destructive' as const, icon: CheckCircle },
      refunded: { variant: 'destructive' as const, icon: CheckCircle },
    };

    const config = statusConfig[status];
    const Icon = config.icon;

    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className="h-3 w-3" />
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const getPaymentStatusBadge = (status: PaymentStatus) => {
    const statusConfig = {
      pending: { variant: 'secondary' as const },
      completed: { variant: 'default' as const },
      failed: { variant: 'destructive' as const },
      refunded: { variant: 'destructive' as const },
    };

    const config = statusConfig[status];

    return (
      <Badge variant={config.variant}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const openOrderDetails = (order: Order) => {
    setSelectedOrder(order);
    setIsDetailDialogOpen(true);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">Loading your orders...</div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center space-x-4">
            <Link to="/dashboard">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Dashboard
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold">My Orders</h1>
              <p className="text-muted-foreground">
                View and track all your orders
              </p>
            </div>
          </div>

          {/* Search */}
          <Card>
            <CardHeader>
              <CardTitle>Search Orders</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="relative max-w-md">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search by order number..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>
            </CardContent>
          </Card>

          {/* Orders List */}
          <div className="space-y-4">
            {filteredOrders.length > 0 ? (
              filteredOrders.map((order) => (
                <Card key={order.id}>
                  <CardContent className="p-6">
                    <div className="flex justify-between items-start mb-4">
                      <div>
                        <h3 className="font-semibold text-lg">{order.order_number}</h3>
                        <p className="text-sm text-muted-foreground">
                          Ordered on {new Date(order.created_at).toLocaleDateString()}
                        </p>
                      </div>
                      <div className="text-right">
                        <div className="font-bold text-lg">${order.total_amount.toFixed(2)}</div>
                        <div className="flex gap-2 mt-1">
                          {getStatusBadge(order.status)}
                          {getPaymentStatusBadge(order.payment_status)}
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                      <div>
                        <Label className="text-sm font-medium">Subtotal</Label>
                        <p className="text-sm">${order.subtotal.toFixed(2)}</p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium">Tax</Label>
                        <p className="text-sm">${order.tax_amount.toFixed(2)}</p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium">Shipping</Label>
                        <p className="text-sm">${order.shipping_amount.toFixed(2)}</p>
                      </div>
                    </div>

                    {order.notes && (
                      <div className="mb-4">
                        <Label className="text-sm font-medium">Notes</Label>
                        <p className="text-sm text-muted-foreground">{order.notes}</p>
                      </div>
                    )}

                    <div className="flex justify-between items-center">
                      <div className="text-sm text-muted-foreground">
                        Last updated: {new Date(order.updated_at).toLocaleDateString()}
                      </div>
                      <div className="flex space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => openOrderDetails(order)}
                        >
                          <Eye className="h-4 w-4 mr-2" />
                          View Details
                        </Button>
                        {order.payment_status === 'completed' && (
                          <Button variant="outline" size="sm">
                            <Download className="h-4 w-4 mr-2" />
                            Invoice
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            ) : (
              <Card>
                <CardContent className="flex flex-col items-center justify-center py-12">
                  <ShoppingCart className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-2">
                    {orders.length === 0 ? 'No orders yet' : 'No orders match your search'}
                  </h3>
                  <p className="text-muted-foreground mb-4">
                    {orders.length === 0 
                      ? 'Start shopping to see your orders here' 
                      : 'Try adjusting your search criteria'
                    }
                  </p>
                  {orders.length === 0 && (
                    <Link to="/shop">
                      <Button>
                        Browse Products
                      </Button>
                    </Link>
                  )}
                </CardContent>
              </Card>
            )}
          </div>

          {/* Order Details Dialog */}
          <Dialog open={isDetailDialogOpen} onOpenChange={setIsDetailDialogOpen}>
            <DialogContent className="sm:max-w-[600px]">
              <DialogHeader>
                <DialogTitle>Order Details</DialogTitle>
                <DialogDescription>
                  {selectedOrder?.order_number}
                </DialogDescription>
              </DialogHeader>
              
              {selectedOrder && (
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm font-medium">Order Status</Label>
                      <div className="mt-1">{getStatusBadge(selectedOrder.status)}</div>
                    </div>
                    <div>
                      <Label className="text-sm font-medium">Payment Status</Label>
                      <div className="mt-1">{getPaymentStatusBadge(selectedOrder.payment_status)}</div>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm font-medium">Order Date</Label>
                      <p className="mt-1">{new Date(selectedOrder.created_at).toLocaleString()}</p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium">Last Updated</Label>
                      <p className="mt-1">{new Date(selectedOrder.updated_at).toLocaleString()}</p>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label className="text-sm font-medium">Order Summary</Label>
                    <div className="bg-muted p-4 rounded-lg space-y-2">
                      <div className="flex justify-between">
                        <span>Subtotal:</span>
                        <span>${selectedOrder.subtotal.toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Tax:</span>
                        <span>${selectedOrder.tax_amount.toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Shipping:</span>
                        <span>${selectedOrder.shipping_amount.toFixed(2)}</span>
                      </div>
                      {selectedOrder.discount_amount > 0 && (
                        <div className="flex justify-between text-green-600">
                          <span>Discount:</span>
                          <span>-${selectedOrder.discount_amount.toFixed(2)}</span>
                        </div>
                      )}
                      <div className="flex justify-between font-bold border-t pt-2">
                        <span>Total:</span>
                        <span>${selectedOrder.total_amount.toFixed(2)}</span>
                      </div>
                    </div>
                  </div>

                  {selectedOrder.notes && (
                    <div>
                      <Label className="text-sm font-medium">Notes</Label>
                      <p className="mt-1 text-sm text-muted-foreground">{selectedOrder.notes}</p>
                    </div>
                  )}
                </div>
              )}

              <DialogFooter>
                <Button variant="outline" onClick={() => setIsDetailDialogOpen(false)}>
                  Close
                </Button>
                {selectedOrder?.payment_status === 'completed' && (
                  <Button>
                    <Download className="h-4 w-4 mr-2" />
                    Download Invoice
                  </Button>
                )}
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>
    </div>
  );
};

export default CustomerOrders;
