-- Admin Setup Script
-- Run this in Supabase SQL Editor to create admin user and policies

-- First, add admin policies for user_profiles
CREATE POLICY IF NOT EXISTS "Ad<PERSON> can view all profiles" ON public.user_profiles
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.user_profiles up 
      WHERE up.id = auth.uid() 
      AND up.role IN ('admin', 'super_admin')
    )
  );

CREATE POLICY IF NOT EXISTS "Ad<PERSON> can update all profiles" ON public.user_profiles
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM public.user_profiles up 
      WHERE up.id = auth.uid() 
      AND up.role IN ('admin', 'super_admin')
    )
  );

CREATE POLICY IF NOT EXISTS "Admins can insert profiles" ON public.user_profiles
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.user_profiles up 
      WHERE up.id = auth.uid() 
      AND up.role IN ('admin', 'super_admin')
    )
    OR auth.uid() = id -- Allow users to create their own profile
  );

-- Create or update admin user profile
-- This assumes the admin user already exists in auth.users
-- If not, you need to create it through Supabase Auth first

DO $$
DECLARE
  admin_user_id uuid;
  admin_email text := '<EMAIL>';
BEGIN
  -- Try to find existing admin user
  SELECT id INTO admin_user_id 
  FROM auth.users 
  WHERE email = admin_email;
  
  -- If admin user exists, create/update their profile
  IF admin_user_id IS NOT NULL THEN
    INSERT INTO public.user_profiles (
      id,
      email,
      first_name,
      last_name,
      role,
      created_at,
      updated_at
    )
    VALUES (
      admin_user_id,
      admin_email,
      'Admin',
      'User',
      'super_admin'::user_role,
      NOW(),
      NOW()
    )
    ON CONFLICT (id) DO UPDATE SET
      role = 'super_admin'::user_role,
      updated_at = NOW();
      
    RAISE NOTICE 'Admin profile created/updated for user ID: %', admin_user_id;
  ELSE
    RAISE NOTICE 'Admin user not found in auth.users. Please create user first.';
  END IF;
END $$;

-- Alternative: If you need to create the auth user as well (use with caution)
-- This creates a user directly in the auth schema
/*
DO $$
DECLARE
  admin_user_id uuid := gen_random_uuid();
  admin_email text := '<EMAIL>';
BEGIN
  -- Insert admin user into auth.users if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM auth.users WHERE email = admin_email) THEN
    INSERT INTO auth.users (
      id,
      instance_id,
      email,
      encrypted_password,
      email_confirmed_at,
      created_at,
      updated_at,
      role,
      aud
    ) VALUES (
      admin_user_id,
      '00000000-0000-0000-0000-000000000000',
      admin_email,
      crypt('admin123456', gen_salt('bf')),
      NOW(),
      NOW(),
      NOW(),
      'authenticated',
      'authenticated'
    );
    
    -- Insert admin identity
    INSERT INTO auth.identities (
      id,
      user_id,
      identity_data,
      provider,
      created_at,
      updated_at
    ) VALUES (
      admin_user_id,
      admin_user_id,
      format('{"sub":"%s","email":"%s"}', admin_user_id, admin_email)::jsonb,
      'email',
      NOW(),
      NOW()
    );
    
    -- Create admin profile
    INSERT INTO public.user_profiles (
      id,
      email,
      first_name,
      last_name,
      role,
      created_at,
      updated_at
    )
    VALUES (
      admin_user_id,
      admin_email,
      'Admin',
      'User',
      'super_admin'::user_role,
      NOW(),
      NOW()
    );
    
    RAISE NOTICE 'Admin user and profile created with ID: %', admin_user_id;
  ELSE
    RAISE NOTICE 'Admin user already exists';
  END IF;
END $$;
*/
